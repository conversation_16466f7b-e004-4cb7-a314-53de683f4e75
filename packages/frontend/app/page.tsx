'use client';

import { useState } from 'react';
import { TranslationLayout } from '@/components/layout/TranslationLayout';
import { ProjectPageResponse, OCRStatus } from '@/types/api';

export default function Home() {
  // Mock data for demonstration
  const [currentProject] = useState<ProjectPageResponse>({
    id: 'demo-project',
    project_id: 'demo-project',
    page_number: 1,
    original_filename: 'sample-manga-page.jpg',
    file_path: '/uploads/sample-manga-page.jpg',
    file_size: 1024000,
    image_width: 800,
    image_height: 1200,
    ocr_status: OCRStatus.PENDING,
    text_region_count: 0,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  });

  // For demo purposes, we'll use a placeholder image
  const demoImageUrl = 'https://via.placeholder.com/800x1200/f3f4f6/9ca3af?text=Manga+Page+Demo';

  return (
    <div className="h-screen">
      <TranslationLayout
        currentProject={currentProject}
        currentPage={currentProject}
        imageUrl={demoImageUrl}
        onProjectChange={(projectId) => console.log('Project changed:', projectId)}
        onPageChange={(pageId) => console.log('Page changed:', pageId)}
      />
    </div>
  );
}
