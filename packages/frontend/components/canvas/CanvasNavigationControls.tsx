'use client';

import React, { useEffect, useCallback } from 'react';
import { CanvasState, CanvasTool } from '@/types/canvas';

interface CanvasNavigationControlsProps {
  canvasState: CanvasState;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onResetZoom: () => void;
  onFitToScreen: () => void;
  onPan: (deltaX: number, deltaY: number) => void;
  onToolChange: (tool: CanvasTool) => void;
  onToggleGrid?: () => void;
  onToggleRulers?: () => void;
  showGrid?: boolean;
  showRulers?: boolean;
  className?: string;
}

export const CanvasNavigationControls: React.FC<CanvasNavigationControlsProps> = ({
  canvasState,
  onZoomIn,
  onZoomOut,
  onResetZoom,
  onFitToScreen,
  onPan,
  onToolChange,
  onToggleGrid,
  onToggleRulers,
  showGrid = false,
  showRulers = false,
  className = ''
}) => {
  // Keyboard shortcuts
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    // Don't trigger shortcuts when typing in inputs
    if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
      return;
    }

    // Prevent default for our shortcuts
    const shortcuts = ['Space', 'Equal', 'Minus', 'Digit0', 'KeyV', 'KeyT', 'KeyS', 'KeyG', 'KeyR'];
    if (shortcuts.includes(e.code) || (e.ctrlKey || e.metaKey)) {
      if (shortcuts.includes(e.code)) {
        e.preventDefault();
      }
    }

    // Handle shortcuts
    switch (e.code) {
      case 'Space':
        if (!e.ctrlKey && !e.metaKey && !e.altKey) {
          onToolChange(CanvasTool.PAN);
        }
        break;
      case 'KeyV':
        onToolChange(CanvasTool.SELECT);
        break;
      case 'KeyT':
        onToolChange(CanvasTool.TEXT_REGION);
        break;
      case 'Equal':
        if (e.ctrlKey || e.metaKey) {
          onZoomIn();
        }
        break;
      case 'Minus':
        if (e.ctrlKey || e.metaKey) {
          onZoomOut();
        }
        break;
      case 'Digit0':
        if (e.ctrlKey || e.metaKey) {
          onResetZoom();
        }
        break;
      case 'KeyG':
        if (onToggleGrid) {
          onToggleGrid();
        }
        break;
      case 'KeyR':
        if (onToggleRulers) {
          onToggleRulers();
        }
        break;
    }

    // Arrow key panning
    const panStep = 20;
    switch (e.code) {
      case 'ArrowUp':
        if (e.shiftKey) {
          onPan(0, panStep);
        }
        break;
      case 'ArrowDown':
        if (e.shiftKey) {
          onPan(0, -panStep);
        }
        break;
      case 'ArrowLeft':
        if (e.shiftKey) {
          onPan(panStep, 0);
        }
        break;
      case 'ArrowRight':
        if (e.shiftKey) {
          onPan(-panStep, 0);
        }
        break;
    }
  }, [onZoomIn, onZoomOut, onResetZoom, onPan, onToolChange, onToggleGrid, onToggleRulers]);

  // Handle key up for space bar
  const handleKeyUp = useCallback((e: KeyboardEvent) => {
    if (e.code === 'Space' && canvasState.selectedTool === CanvasTool.PAN) {
      // Return to previous tool (default to select)
      onToolChange(CanvasTool.SELECT);
    }
  }, [canvasState.selectedTool, onToolChange]);

  // Set up keyboard event listeners
  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, [handleKeyDown, handleKeyUp]);

  const zoomPercentage = Math.round(canvasState.zoom * 100);

  const zoomLevels = [25, 50, 75, 100, 125, 150, 200, 300, 400, 500];

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      {/* Navigation Controls */}
      <div className="p-3 space-y-3">
        {/* Zoom Controls */}
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-2">Zoom</h4>
          <div className="space-y-2">
            {/* Zoom Buttons */}
            <div className="flex items-center space-x-1">
              <button
                onClick={onZoomOut}
                className="flex-1 py-1.5 px-2 text-xs font-medium text-gray-700 bg-gray-50 border border-gray-200 rounded-l-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
                title="Zoom out (Ctrl+-)"
              >
                <svg className="w-3 h-3 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                </svg>
              </button>
              
              <div className="flex-1 py-1.5 px-2 text-xs font-medium text-gray-700 bg-white border-t border-b border-gray-200 text-center min-w-[60px]">
                {zoomPercentage}%
              </div>
              
              <button
                onClick={onZoomIn}
                className="flex-1 py-1.5 px-2 text-xs font-medium text-gray-700 bg-gray-50 border border-gray-200 rounded-r-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
                title="Zoom in (Ctrl++)"
              >
                <svg className="w-3 h-3 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
              </button>
            </div>

            {/* Quick Zoom Levels */}
            <div className="grid grid-cols-5 gap-1">
              {zoomLevels.map((level) => (
                <button
                  key={level}
                  onClick={() => {
                    // This would need to be implemented in the parent component
                    console.log(`Set zoom to ${level}%`);
                  }}
                  className={`py-1 px-1 text-xs border rounded transition-colors ${
                    zoomPercentage === level
                      ? 'border-blue-500 bg-blue-50 text-blue-700'
                      : 'border-gray-200 hover:bg-gray-50'
                  }`}
                >
                  {level}%
                </button>
              ))}
            </div>

            {/* Fit Controls */}
            <div className="grid grid-cols-2 gap-1">
              <button
                onClick={onResetZoom}
                className="py-1.5 px-2 text-xs font-medium text-gray-700 bg-gray-50 border border-gray-200 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
                title="Reset zoom (Ctrl+0)"
              >
                100%
              </button>
              <button
                onClick={onFitToScreen}
                className="py-1.5 px-2 text-xs font-medium text-gray-700 bg-gray-50 border border-gray-200 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
                title="Fit to screen"
              >
                Fit
              </button>
            </div>
          </div>
        </div>

        {/* Tool Selection */}
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-2">Tools</h4>
          <div className="grid grid-cols-3 gap-1">
            {[
              { tool: CanvasTool.SELECT, name: 'Select', icon: '⬆️', shortcut: 'V' },
              { tool: CanvasTool.TEXT_REGION, name: 'Text', icon: '📝', shortcut: 'T' },
              { tool: CanvasTool.PAN, name: 'Pan', icon: '✋', shortcut: 'Space' }
            ].map((toolItem) => (
              <button
                key={toolItem.tool}
                onClick={() => onToolChange(toolItem.tool)}
                className={`flex flex-col items-center justify-center p-2 rounded-md text-xs transition-colors ${
                  canvasState.selectedTool === toolItem.tool
                    ? 'bg-blue-100 text-blue-700 border border-blue-300'
                    : 'bg-gray-50 text-gray-600 border border-gray-200 hover:bg-gray-100'
                }`}
                title={`${toolItem.name} (${toolItem.shortcut})`}
              >
                <span className="text-lg mb-1">{toolItem.icon}</span>
                <span>{toolItem.name}</span>
              </button>
            ))}
          </div>
        </div>

        {/* View Options */}
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-2">View</h4>
          <div className="space-y-1">
            {onToggleGrid && (
              <button
                onClick={onToggleGrid}
                className={`w-full flex items-center justify-between py-1.5 px-2 text-xs rounded-md transition-colors ${
                  showGrid
                    ? 'bg-blue-50 text-blue-700 border border-blue-200'
                    : 'bg-gray-50 text-gray-600 border border-gray-200 hover:bg-gray-100'
                }`}
                title="Toggle grid (G)"
              >
                <span>Grid</span>
                <span className="text-xs opacity-60">G</span>
              </button>
            )}
            
            {onToggleRulers && (
              <button
                onClick={onToggleRulers}
                className={`w-full flex items-center justify-between py-1.5 px-2 text-xs rounded-md transition-colors ${
                  showRulers
                    ? 'bg-blue-50 text-blue-700 border border-blue-200'
                    : 'bg-gray-50 text-gray-600 border border-gray-200 hover:bg-gray-100'
                }`}
                title="Toggle rulers (R)"
              >
                <span>Rulers</span>
                <span className="text-xs opacity-60">R</span>
              </button>
            )}
          </div>
        </div>

        {/* Canvas Info */}
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-2">Position</h4>
          <div className="space-y-1 text-xs text-gray-500">
            <div className="flex justify-between">
              <span>Pan X:</span>
              <span>{Math.round(canvasState.panX)}px</span>
            </div>
            <div className="flex justify-between">
              <span>Pan Y:</span>
              <span>{Math.round(canvasState.panY)}px</span>
            </div>
            <div className="flex justify-between">
              <span>Zoom:</span>
              <span>{zoomPercentage}%</span>
            </div>
          </div>
        </div>

        {/* Keyboard Shortcuts Help */}
        <div className="pt-2 border-t border-gray-200">
          <details className="group">
            <summary className="text-xs font-medium text-gray-700 cursor-pointer hover:text-gray-900">
              Keyboard Shortcuts
            </summary>
            <div className="mt-2 space-y-1 text-xs text-gray-500">
              <div className="flex justify-between">
                <span>Select Tool:</span>
                <span className="font-mono">V</span>
              </div>
              <div className="flex justify-between">
                <span>Text Tool:</span>
                <span className="font-mono">T</span>
              </div>
              <div className="flex justify-between">
                <span>Pan Tool:</span>
                <span className="font-mono">Space</span>
              </div>
              <div className="flex justify-between">
                <span>Zoom In:</span>
                <span className="font-mono">Ctrl +</span>
              </div>
              <div className="flex justify-between">
                <span>Zoom Out:</span>
                <span className="font-mono">Ctrl -</span>
              </div>
              <div className="flex justify-between">
                <span>Reset Zoom:</span>
                <span className="font-mono">Ctrl 0</span>
              </div>
              <div className="flex justify-between">
                <span>Pan Canvas:</span>
                <span className="font-mono">Shift + ↑↓←→</span>
              </div>
              {onToggleGrid && (
                <div className="flex justify-between">
                  <span>Toggle Grid:</span>
                  <span className="font-mono">G</span>
                </div>
              )}
              {onToggleRulers && (
                <div className="flex justify-between">
                  <span>Toggle Rulers:</span>
                  <span className="font-mono">R</span>
                </div>
              )}
            </div>
          </details>
        </div>
      </div>
    </div>
  );
};

export default CanvasNavigationControls;
