'use client';

import React, { useState, useCallback } from 'react';
import { CanvasTextRegion } from '@/types/canvas';
import { TextRegionType } from '@/types/api';

interface TextRegionEditorProps {
  region: CanvasTextRegion | null;
  onUpdate: (regionId: string, updates: Partial<CanvasTextRegion>) => void;
  onDelete: (regionId: string) => void;
  onClose: () => void;
  className?: string;
}

export const TextRegionEditor: React.FC<TextRegionEditorProps> = ({
  region,
  onUpdate,
  onDelete,
  onClose,
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState<'text' | 'style' | 'position'>('text');

  const handleTextChange = useCallback((field: 'original_text' | 'translated_text', value: string) => {
    if (!region) return;
    onUpdate(region.id, { [field]: value });
  }, [region, onUpdate]);

  const handleStyleChange = useCallback((field: string, value: any) => {
    if (!region) return;
    onUpdate(region.id, { [field]: value });
  }, [region, onUpdate]);

  const handlePositionChange = useCallback((field: 'x' | 'y' | 'width' | 'height', value: number) => {
    if (!region) return;
    const clampedValue = Math.max(0, Math.min(1, value));
    onUpdate(region.id, { [field]: clampedValue });
  }, [region, onUpdate]);

  const handleDelete = useCallback(() => {
    if (!region) return;
    if (confirm('Are you sure you want to delete this text region?')) {
      onDelete(region.id);
      onClose();
    }
  }, [region, onDelete, onClose]);

  if (!region) {
    return (
      <div className={`bg-white border border-gray-200 rounded-lg shadow-sm p-4 ${className}`}>
        <div className="text-center text-gray-500">
          <svg className="w-12 h-12 mx-auto mb-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <p className="text-sm">Select a text region to edit</p>
        </div>
      </div>
    );
  }

  const regionTypes = [
    { value: TextRegionType.SPEECH_BUBBLE, label: 'Speech Bubble' },
    { value: TextRegionType.THOUGHT_BUBBLE, label: 'Thought Bubble' },
    { value: TextRegionType.NARRATION, label: 'Narration' },
    { value: TextRegionType.SOUND_EFFECT, label: 'Sound Effect' },
    { value: TextRegionType.SIGN, label: 'Sign' },
    { value: TextRegionType.OTHER, label: 'Other' }
  ];

  const fontFamilies = [
    'Arial', 'Helvetica', 'Times New Roman', 'Georgia', 'Verdana', 
    'Tahoma', 'Comic Sans MS', 'Impact', 'Trebuchet MS', 'Courier New'
  ];

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900">Edit Text Region</h3>
        <div className="flex space-x-2">
          <button
            onClick={handleDelete}
            className="p-1 text-red-600 hover:text-red-700 hover:bg-red-50 rounded"
            title="Delete region"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </button>
          <button
            onClick={onClose}
            className="p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded"
            title="Close editor"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-gray-200">
        {[
          { id: 'text', label: 'Text', icon: '📝' },
          { id: 'style', label: 'Style', icon: '🎨' },
          { id: 'position', label: 'Position', icon: '📐' }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex-1 px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeTab === tab.id
                ? 'border-blue-500 text-blue-600 bg-blue-50'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
            }`}
          >
            <span className="mr-1">{tab.icon}</span>
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="p-4 space-y-4">
        {activeTab === 'text' && (
          <>
            {/* Region Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Region Type
              </label>
              <select
                value={region.region_type}
                onChange={(e) => handleStyleChange('region_type', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {regionTypes.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Original Text */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Original Text
              </label>
              <textarea
                value={region.original_text || ''}
                onChange={(e) => handleTextChange('original_text', e.target.value)}
                placeholder="Enter original text..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Translated Text */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Translated Text
              </label>
              <textarea
                value={region.translated_text || ''}
                onChange={(e) => handleTextChange('translated_text', e.target.value)}
                placeholder="Enter translated text..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Translation Status */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Translation Status
              </label>
              <select
                value={region.translation_status}
                onChange={(e) => handleStyleChange('translation_status', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="pending">Pending</option>
                <option value="in_progress">In Progress</option>
                <option value="completed">Completed</option>
                <option value="failed">Failed</option>
              </select>
            </div>
          </>
        )}

        {activeTab === 'style' && (
          <>
            {/* Font Family */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Font Family
              </label>
              <select
                value={region.font_family || 'Arial'}
                onChange={(e) => handleStyleChange('font_family', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {fontFamilies.map((font) => (
                  <option key={font} value={font}>
                    {font}
                  </option>
                ))}
              </select>
            </div>

            {/* Font Size */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Font Size
              </label>
              <input
                type="number"
                min="8"
                max="72"
                value={region.font_size || 14}
                onChange={(e) => handleStyleChange('font_size', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Font Color */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Font Color
              </label>
              <div className="flex space-x-2">
                <input
                  type="color"
                  value={region.font_color || '#000000'}
                  onChange={(e) => handleStyleChange('font_color', e.target.value)}
                  className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                />
                <input
                  type="text"
                  value={region.font_color || '#000000'}
                  onChange={(e) => handleStyleChange('font_color', e.target.value)}
                  placeholder="#000000"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            {/* Background Color */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Background Color
              </label>
              <div className="flex space-x-2">
                <input
                  type="color"
                  value={region.background_color === 'transparent' ? '#ffffff' : region.background_color || '#ffffff'}
                  onChange={(e) => handleStyleChange('background_color', e.target.value)}
                  className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                />
                <input
                  type="text"
                  value={region.background_color || 'transparent'}
                  onChange={(e) => handleStyleChange('background_color', e.target.value)}
                  placeholder="transparent or #ffffff"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <button
                onClick={() => handleStyleChange('background_color', 'transparent')}
                className="mt-1 text-xs text-blue-600 hover:text-blue-700"
              >
                Set transparent
              </button>
            </div>
          </>
        )}

        {activeTab === 'position' && (
          <>
            {/* Position and Size */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  X Position
                </label>
                <input
                  type="number"
                  min="0"
                  max="1"
                  step="0.01"
                  value={region.x}
                  onChange={(e) => handlePositionChange('x', parseFloat(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Y Position
                </label>
                <input
                  type="number"
                  min="0"
                  max="1"
                  step="0.01"
                  value={region.y}
                  onChange={(e) => handlePositionChange('y', parseFloat(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Width
                </label>
                <input
                  type="number"
                  min="0.01"
                  max="1"
                  step="0.01"
                  value={region.width}
                  onChange={(e) => handlePositionChange('width', parseFloat(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Height
                </label>
                <input
                  type="number"
                  min="0.01"
                  max="1"
                  step="0.01"
                  value={region.height}
                  onChange={(e) => handlePositionChange('height', parseFloat(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            {/* Confidence Score (read-only) */}
            {region.confidence_score !== undefined && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  OCR Confidence Score
                </label>
                <div className="flex items-center space-x-2">
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{ width: `${(region.confidence_score || 0) * 100}%` }}
                    ></div>
                  </div>
                  <span className="text-sm text-gray-600">
                    {Math.round((region.confidence_score || 0) * 100)}%
                  </span>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default TextRegionEditor;
