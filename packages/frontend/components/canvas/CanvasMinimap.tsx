'use client';

import React, { useRef, useEffect, useState, useCallback } from 'react';
import { CanvasState, CanvasImageInfo, CanvasTextRegion } from '@/types/canvas';

interface CanvasMinimapProps {
  canvasState: CanvasState;
  imageInfo: CanvasImageInfo | null;
  textRegions: CanvasTextRegion[];
  onViewportChange: (x: number, y: number) => void;
  width?: number;
  height?: number;
  className?: string;
}

export const CanvasMinimap: React.FC<CanvasMinimapProps> = ({
  canvasState,
  imageInfo,
  textRegions,
  onViewportChange,
  width = 200,
  height = 150,
  className = ''
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [scale, setScale] = useState(1);

  // Calculate scale factor to fit image in minimap
  useEffect(() => {
    if (imageInfo) {
      const scaleX = width / imageInfo.originalWidth;
      const scaleY = height / imageInfo.originalHeight;
      setScale(Math.min(scaleX, scaleY));
    }
  }, [imageInfo, width, height]);

  // Draw minimap
  const drawMinimap = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas || !imageInfo) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // Calculate scaled dimensions
    const scaledWidth = imageInfo.originalWidth * scale;
    const scaledHeight = imageInfo.originalHeight * scale;
    const offsetX = (width - scaledWidth) / 2;
    const offsetY = (height - scaledHeight) / 2;

    // Draw image background
    ctx.fillStyle = '#f3f4f6';
    ctx.fillRect(offsetX, offsetY, scaledWidth, scaledHeight);

    // Draw image border
    ctx.strokeStyle = '#d1d5db';
    ctx.lineWidth = 1;
    ctx.strokeRect(offsetX, offsetY, scaledWidth, scaledHeight);

    // Draw text regions
    textRegions.forEach((region) => {
      const regionX = offsetX + region.x * scaledWidth;
      const regionY = offsetY + region.y * scaledHeight;
      const regionWidth = region.width * scaledWidth;
      const regionHeight = region.height * scaledHeight;

      // Fill region
      ctx.fillStyle = region.isSelected ? '#3b82f6' : '#ef4444';
      ctx.globalAlpha = 0.3;
      ctx.fillRect(regionX, regionY, regionWidth, regionHeight);

      // Border region
      ctx.globalAlpha = 1;
      ctx.strokeStyle = region.isSelected ? '#3b82f6' : '#ef4444';
      ctx.lineWidth = 1;
      ctx.strokeRect(regionX, regionY, regionWidth, regionHeight);
    });

    // Draw viewport indicator
    const viewportScale = 1 / canvasState.zoom;
    const viewportWidth = scaledWidth * viewportScale;
    const viewportHeight = scaledHeight * viewportScale;
    
    // Calculate viewport position based on pan
    const viewportX = offsetX - (canvasState.panX * scale);
    const viewportY = offsetY - (canvasState.panY * scale);

    // Clamp viewport to image bounds
    const clampedX = Math.max(offsetX, Math.min(offsetX + scaledWidth - viewportWidth, viewportX));
    const clampedY = Math.max(offsetY, Math.min(offsetY + scaledHeight - viewportHeight, viewportY));

    // Draw viewport rectangle
    ctx.strokeStyle = '#1f2937';
    ctx.lineWidth = 2;
    ctx.setLineDash([4, 4]);
    ctx.strokeRect(clampedX, clampedY, viewportWidth, viewportHeight);
    ctx.setLineDash([]);

    // Draw viewport fill
    ctx.fillStyle = '#1f2937';
    ctx.globalAlpha = 0.1;
    ctx.fillRect(clampedX, clampedY, viewportWidth, viewportHeight);
    ctx.globalAlpha = 1;
  }, [canvasState, imageInfo, textRegions, scale, width, height]);

  // Redraw when dependencies change
  useEffect(() => {
    drawMinimap();
  }, [drawMinimap]);

  // Handle mouse events for viewport dragging
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (!imageInfo) return;

    setIsDragging(true);
    
    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return;

    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // Convert minimap coordinates to canvas coordinates
    const scaledWidth = imageInfo.originalWidth * scale;
    const scaledHeight = imageInfo.originalHeight * scale;
    const offsetX = (width - scaledWidth) / 2;
    const offsetY = (height - scaledHeight) / 2;

    // Calculate new pan position
    const relativeX = (x - offsetX) / scaledWidth;
    const relativeY = (y - offsetY) / scaledHeight;

    const newPanX = -(relativeX * imageInfo.originalWidth - imageInfo.displayWidth / 2);
    const newPanY = -(relativeY * imageInfo.originalHeight - imageInfo.displayHeight / 2);

    onViewportChange(newPanX, newPanY);
  }, [imageInfo, scale, width, height, onViewportChange]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!isDragging || !imageInfo) return;

    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return;

    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // Convert minimap coordinates to canvas coordinates
    const scaledWidth = imageInfo.originalWidth * scale;
    const scaledHeight = imageInfo.originalHeight * scale;
    const offsetX = (width - scaledWidth) / 2;
    const offsetY = (height - scaledHeight) / 2;

    // Calculate new pan position
    const relativeX = (x - offsetX) / scaledWidth;
    const relativeY = (y - offsetY) / scaledHeight;

    const newPanX = -(relativeX * imageInfo.originalWidth - imageInfo.displayWidth / 2);
    const newPanY = -(relativeY * imageInfo.originalHeight - imageInfo.displayHeight / 2);

    onViewportChange(newPanX, newPanY);
  }, [isDragging, imageInfo, scale, width, height, onViewportChange]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Global mouse up listener
  useEffect(() => {
    if (isDragging) {
      const handleGlobalMouseUp = () => setIsDragging(false);
      document.addEventListener('mouseup', handleGlobalMouseUp);
      return () => document.removeEventListener('mouseup', handleGlobalMouseUp);
    }
  }, [isDragging]);

  if (!imageInfo) {
    return (
      <div className={`bg-gray-100 border border-gray-200 rounded-lg flex items-center justify-center ${className}`} style={{ width, height }}>
        <div className="text-center text-gray-500">
          <svg className="w-8 h-8 mx-auto mb-1 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          <p className="text-xs">No image</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      {/* Header */}
      <div className="px-3 py-2 border-b border-gray-200">
        <h4 className="text-sm font-medium text-gray-700">Navigator</h4>
      </div>

      {/* Minimap Canvas */}
      <div className="p-2">
        <canvas
          ref={canvasRef}
          width={width}
          height={height}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          className={`border border-gray-200 rounded cursor-pointer ${isDragging ? 'cursor-grabbing' : 'cursor-grab'}`}
          style={{ width, height }}
        />
      </div>

      {/* Info */}
      <div className="px-3 py-2 border-t border-gray-200 text-xs text-gray-500">
        <div className="flex justify-between items-center">
          <span>Zoom: {Math.round(canvasState.zoom * 100)}%</span>
          <span>{textRegions.length} regions</span>
        </div>
      </div>

      {/* Legend */}
      <div className="px-3 py-2 border-t border-gray-200">
        <div className="flex items-center justify-between text-xs">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-2 bg-red-400 rounded"></div>
            <span className="text-gray-600">Text Regions</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-2 bg-blue-400 rounded"></div>
            <span className="text-gray-600">Selected</span>
          </div>
        </div>
        <div className="flex items-center space-x-2 mt-1">
          <div className="w-3 h-2 border-2 border-gray-800 border-dashed rounded"></div>
          <span className="text-gray-600">Viewport</span>
        </div>
      </div>
    </div>
  );
};

export default CanvasMinimap;
