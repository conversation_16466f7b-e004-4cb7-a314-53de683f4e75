'use client';

import React, { useState } from 'react';
import { CanvasState, CanvasTool } from '@/types/canvas';

interface CanvasToolbarProps {
  canvasState: CanvasState;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onResetZoom: () => void;
  onFitToScreen: () => void;
  onToolChange: (tool: CanvasTool) => void;
  onUndo?: () => void;
  onRedo?: () => void;
  onSave?: () => void;
  onExport?: () => void;
  canUndo?: boolean;
  canRedo?: boolean;
  className?: string;
}

export const CanvasToolbar: React.FC<CanvasToolbarProps> = ({
  canvasState,
  onZoomIn,
  onZoomOut,
  onResetZoom,
  onFitToScreen,
  onToolChange,
  onUndo,
  onRedo,
  onSave,
  onExport,
  canUndo = false,
  canRedo = false,
  className = ''
}) => {
  const [showZoomMenu, setShowZoomMenu] = useState(false);

  const zoomPercentage = Math.round(canvasState.zoom * 100);

  const tools = [
    {
      id: CanvasTool.SELECT,
      name: 'Select',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
        </svg>
      ),
      shortcut: 'V'
    },
    {
      id: CanvasTool.TEXT_REGION,
      name: 'Text Region',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      shortcut: 'T'
    },
    {
      id: CanvasTool.PAN,
      name: 'Pan',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16l-4-4m0 0l4-4m-4 4h18M17 8l4 4m0 0l-4 4m4-4H3" />
        </svg>
      ),
      shortcut: 'H'
    }
  ];

  const zoomLevels = [25, 50, 75, 100, 125, 150, 200, 300, 400, 500];

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      <div className="flex items-center divide-x divide-gray-200">
        {/* Tools Section */}
        <div className="flex items-center px-3 py-2">
          {tools.map((tool) => (
            <button
              key={tool.id}
              onClick={() => onToolChange(tool.id)}
              className={`p-2 rounded-md transition-colors ${
                canvasState.selectedTool === tool.id
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
              title={`${tool.name} (${tool.shortcut})`}
            >
              {tool.icon}
            </button>
          ))}
        </div>

        {/* History Section */}
        {(onUndo || onRedo) && (
          <div className="flex items-center px-3 py-2">
            {onUndo && (
              <button
                onClick={onUndo}
                disabled={!canUndo}
                className="p-2 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                title="Undo (Ctrl+Z)"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                </svg>
              </button>
            )}
            {onRedo && (
              <button
                onClick={onRedo}
                disabled={!canRedo}
                className="p-2 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                title="Redo (Ctrl+Y)"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 10h-10a8 8 0 00-8 8v2m18-10l-6-6m6 6l-6 6" />
                </svg>
              </button>
            )}
          </div>
        )}

        {/* Zoom Section */}
        <div className="flex items-center px-3 py-2">
          <button
            onClick={onZoomOut}
            className="p-2 rounded-md transition-colors text-gray-600 hover:text-gray-900 hover:bg-gray-100"
            title="Zoom Out (Ctrl+-)"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM13 10H7" />
            </svg>
          </button>

          <div className="relative">
            <button
              onClick={() => setShowZoomMenu(!showZoomMenu)}
              className="px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-md min-w-[60px]"
              title="Zoom Level"
            >
              {zoomPercentage}%
            </button>

            {showZoomMenu && (
              <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                <div className="py-1">
                  {zoomLevels.map((level) => (
                    <button
                      key={level}
                      onClick={() => {
                        // This would need to be implemented in the parent component
                        console.log(`Set zoom to ${level}%`);
                        setShowZoomMenu(false);
                      }}
                      className={`block w-full text-left px-3 py-1 text-sm hover:bg-gray-100 ${
                        zoomPercentage === level ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                      }`}
                    >
                      {level}%
                    </button>
                  ))}
                  <div className="border-t border-gray-200 my-1"></div>
                  <button
                    onClick={() => {
                      onFitToScreen();
                      setShowZoomMenu(false);
                    }}
                    className="block w-full text-left px-3 py-1 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    Fit to Screen
                  </button>
                  <button
                    onClick={() => {
                      onResetZoom();
                      setShowZoomMenu(false);
                    }}
                    className="block w-full text-left px-3 py-1 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    Actual Size
                  </button>
                </div>
              </div>
            )}
          </div>

          <button
            onClick={onZoomIn}
            className="p-2 rounded-md transition-colors text-gray-600 hover:text-gray-900 hover:bg-gray-100"
            title="Zoom In (Ctrl++)"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
            </svg>
          </button>
        </div>

        {/* View Controls */}
        <div className="flex items-center px-3 py-2">
          <button
            onClick={onFitToScreen}
            className="p-2 rounded-md transition-colors text-gray-600 hover:text-gray-900 hover:bg-gray-100"
            title="Fit to Screen"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
            </svg>
          </button>

          <button
            onClick={onResetZoom}
            className="p-2 rounded-md transition-colors text-gray-600 hover:text-gray-900 hover:bg-gray-100"
            title="Reset View (Ctrl+0)"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
        </div>

        {/* Actions Section */}
        {(onSave || onExport) && (
          <div className="flex items-center px-3 py-2">
            {onSave && (
              <button
                onClick={onSave}
                className="p-2 rounded-md transition-colors text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                title="Save (Ctrl+S)"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                </svg>
              </button>
            )}
            {onExport && (
              <button
                onClick={onExport}
                className="p-2 rounded-md transition-colors text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                title="Export"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </button>
            )}
          </div>
        )}
      </div>

      {/* Click outside to close zoom menu */}
      {showZoomMenu && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setShowZoomMenu(false)}
        />
      )}
    </div>
  );
};

export default CanvasToolbar;
