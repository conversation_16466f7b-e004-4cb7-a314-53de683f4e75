'use client';

import React, { useCallback, useEffect, useState } from 'react';
import * as fabric from 'fabric';
import {
  CanvasTextRegion,
  CanvasImageInfo,
  DEFAULT_CANVAS_CONFIG,
  DEFAULT_FONT_CONFIG
} from '@/types/canvas';
import { TextRegionType } from '@/types/api';

interface TextRegionManagerProps {
  canvas: fabric.Canvas | null;
  imageInfo: CanvasImageInfo | null;
  textRegions: CanvasTextRegion[];
  onRegionCreate: (region: Partial<CanvasTextRegion>) => void;
  onRegionUpdate: (regionId: string, updates: Partial<CanvasTextRegion>) => void;
  onRegionDelete: (regionId: string) => void;
  onRegionSelect: (regionId: string) => void;
  selectedRegionId?: string;
}

export const TextRegionManager: React.FC<TextRegionManagerProps> = ({
  canvas,
  imageInfo,
  textRegions,
  onRegionCreate,
  onRegionUpdate,
  onRegionDelete,
  onRegionSelect,
  selectedRegionId
}) => {
  const [fabricObjects, setFabricObjects] = useState<Map<string, fabric.Group>>(new Map());

  // Convert normalized coordinates to canvas coordinates
  const convertNormalizedToCanvas = useCallback((region: { x: number; y: number; width: number; height: number }) => {
    if (!imageInfo || !canvas) return { left: 0, top: 0, width: 0, height: 0 };

    // Find the image object on canvas
    const imageObject = canvas.getObjects().find(obj => (obj as any).isImage) as fabric.FabricImage;
    if (!imageObject) return { left: 0, top: 0, width: 0, height: 0 };

    const imageLeft = imageObject.left || 0;
    const imageTop = imageObject.top || 0;
    const imageWidth = (imageObject.width || 1) * (imageObject.scaleX || 1);
    const imageHeight = (imageObject.height || 1) * (imageObject.scaleY || 1);

    return {
      left: imageLeft + region.x * imageWidth,
      top: imageTop + region.y * imageHeight,
      width: region.width * imageWidth,
      height: region.height * imageHeight
    };
  }, [imageInfo, canvas]);

  // Convert canvas coordinates to normalized coordinates
  const convertCanvasToNormalized = useCallback((obj: fabric.Object) => {
    if (!imageInfo || !canvas) return { x: 0, y: 0, width: 0, height: 0 };

    const imageObject = canvas.getObjects().find(obj => (obj as any).isImage) as fabric.FabricImage;
    if (!imageObject) return { x: 0, y: 0, width: 0, height: 0 };

    const imageLeft = imageObject.left || 0;
    const imageTop = imageObject.top || 0;
    const imageWidth = (imageObject.width || 1) * (imageObject.scaleX || 1);
    const imageHeight = (imageObject.height || 1) * (imageObject.scaleY || 1);

    const left = obj.left || 0;
    const top = obj.top || 0;
    const width = (obj.width || 0) * (obj.scaleX || 1);
    const height = (obj.height || 0) * (obj.scaleY || 1);

    return {
      x: (left - imageLeft) / imageWidth,
      y: (top - imageTop) / imageHeight,
      width: width / imageWidth,
      height: height / imageHeight
    };
  }, [imageInfo, canvas]);

  // Create a fabric group for a text region
  const createRegionGroup = useCallback((region: CanvasTextRegion) => {
    if (!canvas) return null;

    const canvasCoords = convertNormalizedToCanvas(region);
    
    // Create background rectangle
    const rect = new fabric.Rect({
      left: 0,
      top: 0,
      width: canvasCoords.width,
      height: canvasCoords.height,
      fill: region.background_color === 'transparent' ? 'transparent' : region.background_color || 'rgba(59, 130, 246, 0.2)',
      stroke: region.isSelected ? DEFAULT_CANVAS_CONFIG.selectedRegionColor : region.borderColor || DEFAULT_CANVAS_CONFIG.defaultRegionColor,
      strokeWidth: DEFAULT_CANVAS_CONFIG.regionBorderWidth,
      strokeDashArray: region.isSelected ? [] : [5, 5],
      rx: 4,
      ry: 4
    });

    const objects: fabric.Object[] = [rect];

    // Add text if available
    if (region.translated_text || region.original_text) {
      const text = new fabric.FabricText(region.translated_text || region.original_text || '', {
        left: canvasCoords.width / 2,
        top: canvasCoords.height / 2,
        fontSize: region.font_size || DEFAULT_FONT_CONFIG.size,
        fontFamily: region.font_family || DEFAULT_FONT_CONFIG.family,
        fill: region.font_color || DEFAULT_FONT_CONFIG.color,
        textAlign: 'center',
        originX: 'center',
        originY: 'center',
        splitByGrapheme: true
      });

      objects.push(text);
    }

    // Create group
    const group = new fabric.Group(objects, {
      left: canvasCoords.left,
      top: canvasCoords.top,
      selectable: true,
      evented: true,
      hasControls: true,
      hasBorders: true,
      borderColor: region.isSelected ? DEFAULT_CANVAS_CONFIG.selectedRegionColor : DEFAULT_CANVAS_CONFIG.defaultRegionColor,
      cornerColor: DEFAULT_CANVAS_CONFIG.selectedRegionColor,
      cornerStyle: 'circle',
      transparentCorners: false
    });

    // Store region data
    (group as any).regionId = region.id;
    (group as any).regionData = region;

    return group;
  }, [canvas, convertNormalizedToCanvas]);

  // Add text region to canvas
  const addTextRegion = useCallback((region: CanvasTextRegion) => {
    if (!canvas) return;

    const group = createRegionGroup(region);
    if (!group) return;

    // Set up event handlers
    group.on('selected', () => {
      onRegionSelect(region.id);
    });

    group.on('modified', () => {
      const updatedCoords = convertCanvasToNormalized(group);
      onRegionUpdate(region.id, updatedCoords);
    });

    group.on('moving', () => {
      // Update coordinates in real-time during movement
      const updatedCoords = convertCanvasToNormalized(group);
      onRegionUpdate(region.id, updatedCoords);
    });

    group.on('scaling', () => {
      // Update coordinates in real-time during scaling
      const updatedCoords = convertCanvasToNormalized(group);
      onRegionUpdate(region.id, updatedCoords);
    });

    canvas.add(group);
    setFabricObjects(prev => new Map(prev.set(region.id, group)));
    canvas.renderAll();
  }, [canvas, createRegionGroup, convertCanvasToNormalized, onRegionSelect, onRegionUpdate]);

  // Remove text region from canvas
  const removeTextRegion = useCallback((regionId: string) => {
    if (!canvas) return;

    const group = fabricObjects.get(regionId);
    if (group) {
      canvas.remove(group);
      setFabricObjects(prev => {
        const newMap = new Map(prev);
        newMap.delete(regionId);
        return newMap;
      });
      canvas.renderAll();
    }
  }, [canvas, fabricObjects]);

  // Update text region
  const updateTextRegion = useCallback((regionId: string, updates: Partial<CanvasTextRegion>) => {
    if (!canvas) return;

    const group = fabricObjects.get(regionId);
    if (!group) return;

    const region = textRegions.find(r => r.id === regionId);
    if (!region) return;

    const updatedRegion = { ...region, ...updates };

    // Remove old group and create new one with updates
    canvas.remove(group);
    const newGroup = createRegionGroup(updatedRegion);
    
    if (newGroup) {
      // Set up event handlers for new group
      newGroup.on('selected', () => {
        onRegionSelect(regionId);
      });

      newGroup.on('modified', () => {
        const updatedCoords = convertCanvasToNormalized(newGroup);
        onRegionUpdate(regionId, updatedCoords);
      });

      newGroup.on('moving', () => {
        const updatedCoords = convertCanvasToNormalized(newGroup);
        onRegionUpdate(regionId, updatedCoords);
      });

      newGroup.on('scaling', () => {
        const updatedCoords = convertCanvasToNormalized(newGroup);
        onRegionUpdate(regionId, updatedCoords);
      });

      canvas.add(newGroup);
      setFabricObjects(prev => new Map(prev.set(regionId, newGroup)));
      canvas.renderAll();
    }
  }, [canvas, fabricObjects, textRegions, createRegionGroup, convertCanvasToNormalized, onRegionSelect, onRegionUpdate]);

  // Select text region
  const selectTextRegion = useCallback((regionId: string) => {
    if (!canvas) return;

    const group = fabricObjects.get(regionId);
    if (group) {
      canvas.setActiveObject(group);
      canvas.renderAll();
    }
  }, [canvas, fabricObjects]);

  // Clear selection
  const clearSelection = useCallback(() => {
    if (!canvas) return;

    canvas.discardActiveObject();
    canvas.renderAll();
  }, [canvas]);

  // Handle region type change
  const changeRegionType = useCallback((regionId: string, newType: TextRegionType) => {
    const region = textRegions.find(r => r.id === regionId);
    if (!region) return;

    onRegionUpdate(regionId, { region_type: newType });
  }, [textRegions, onRegionUpdate]);

  // Handle text content change
  const updateRegionText = useCallback((regionId: string, text: string, isTranslated: boolean = true) => {
    const updates = isTranslated 
      ? { translated_text: text }
      : { original_text: text };
    
    onRegionUpdate(regionId, updates);
  }, [onRegionUpdate]);

  // Handle font styling changes
  const updateRegionFont = useCallback((regionId: string, fontUpdates: {
    font_family?: string;
    font_size?: number;
    font_color?: string;
    background_color?: string;
  }) => {
    onRegionUpdate(regionId, fontUpdates);
  }, [onRegionUpdate]);

  // Sync text regions with canvas
  useEffect(() => {
    if (!canvas) return;

    // Remove regions that no longer exist
    const currentRegionIds = new Set(textRegions.map(r => r.id));
    fabricObjects.forEach((group, regionId) => {
      if (!currentRegionIds.has(regionId)) {
        removeTextRegion(regionId);
      }
    });

    // Add or update existing regions
    textRegions.forEach(region => {
      const existingGroup = fabricObjects.get(region.id);
      if (existingGroup) {
        // Update existing region if needed
        const currentData = (existingGroup as any).regionData;
        if (JSON.stringify(currentData) !== JSON.stringify(region)) {
          updateTextRegion(region.id, region);
        }
      } else {
        // Add new region
        addTextRegion(region);
      }
    });
  }, [textRegions, canvas, fabricObjects, addTextRegion, removeTextRegion, updateTextRegion]);

  // Handle selection changes
  useEffect(() => {
    if (!canvas) return;

    const handleSelection = () => {
      const activeObject = canvas.getActiveObject();
      if (activeObject && (activeObject as any).regionId) {
        onRegionSelect((activeObject as any).regionId);
      }
    };

    const handleSelectionCleared = () => {
      // Don't automatically clear selection - let parent component handle it
    };

    canvas.on('selection:created', handleSelection);
    canvas.on('selection:updated', handleSelection);
    canvas.on('selection:cleared', handleSelectionCleared);

    return () => {
      canvas.off('selection:created', handleSelection);
      canvas.off('selection:updated', handleSelection);
      canvas.off('selection:cleared', handleSelectionCleared);
    };
  }, [canvas, onRegionSelect]);

  // Sync selected region with canvas
  useEffect(() => {
    if (selectedRegionId) {
      selectTextRegion(selectedRegionId);
    } else {
      clearSelection();
    }
  }, [selectedRegionId, selectTextRegion, clearSelection]);

  // Public API for external use
  const regionManager = {
    addRegion: addTextRegion,
    removeRegion: removeTextRegion,
    updateRegion: updateTextRegion,
    selectRegion: selectTextRegion,
    clearSelection,
    changeRegionType,
    updateRegionText,
    updateRegionFont,
    getRegionGroup: (regionId: string) => fabricObjects.get(regionId),
    getAllRegionGroups: () => Array.from(fabricObjects.values())
  };

  return null; // This is a logic-only component
};

export default TextRegionManager;
