'use client';

import React from 'react';
import { CanvasTool, CanvasState } from '@/types/canvas';

interface CanvasControlsProps {
  canvasState: CanvasState;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onResetZoom: () => void;
  onToolChange: (tool: CanvasTool) => void;
  className?: string;
}

export const CanvasControls: React.FC<CanvasControlsProps> = ({
  canvasState,
  onZoomIn,
  onZoomOut,
  onResetZoom,
  onToolChange,
  className = ''
}) => {
  const zoomPercentage = Math.round(canvasState.zoom * 100);

  const tools = [
    {
      id: CanvasTool.SELECT,
      name: 'Select',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.121 2.122" />
        </svg>
      ),
      description: 'Select and move objects'
    },
    {
      id: CanvasTool.TEXT_REGION,
      name: 'Text Region',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      description: 'Create text regions'
    },
    {
      id: CanvasTool.PAN,
      name: 'Pan',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16l-4-4m0 0l4-4m-4 4h18" />
        </svg>
      ),
      description: 'Pan around the canvas'
    }
  ];

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm p-3 ${className}`}>
      {/* Tool Selection */}
      <div className="mb-4">
        <h3 className="text-sm font-medium text-gray-700 mb-2">Tools</h3>
        <div className="grid grid-cols-3 gap-1">
          {tools.map((tool) => (
            <button
              key={tool.id}
              onClick={() => onToolChange(tool.id)}
              className={`
                flex flex-col items-center justify-center p-2 rounded-md text-xs transition-colors
                ${canvasState.selectedTool === tool.id
                  ? 'bg-blue-100 text-blue-700 border border-blue-300'
                  : 'bg-gray-50 text-gray-600 border border-gray-200 hover:bg-gray-100'
                }
              `}
              title={tool.description}
            >
              {tool.icon}
              <span className="mt-1">{tool.name}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Zoom Controls */}
      <div className="mb-4">
        <h3 className="text-sm font-medium text-gray-700 mb-2">Zoom</h3>
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-500">Level:</span>
            <span className="text-sm font-medium text-gray-700">{zoomPercentage}%</span>
          </div>
          
          <div className="flex space-x-1">
            <button
              onClick={onZoomOut}
              className="flex-1 py-1.5 px-2 text-xs font-medium text-gray-700 bg-gray-50 border border-gray-200 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
              title="Zoom out"
            >
              <svg className="w-3 h-3 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
              </svg>
            </button>
            
            <button
              onClick={onResetZoom}
              className="flex-1 py-1.5 px-2 text-xs font-medium text-gray-700 bg-gray-50 border border-gray-200 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
              title="Reset zoom"
            >
              Fit
            </button>
            
            <button
              onClick={onZoomIn}
              className="flex-1 py-1.5 px-2 text-xs font-medium text-gray-700 bg-gray-50 border border-gray-200 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
              title="Zoom in"
            >
              <svg className="w-3 h-3 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Canvas Info */}
      <div>
        <h3 className="text-sm font-medium text-gray-700 mb-2">Canvas</h3>
        <div className="space-y-1 text-xs text-gray-500">
          <div className="flex justify-between">
            <span>Pan X:</span>
            <span>{Math.round(canvasState.panX)}px</span>
          </div>
          <div className="flex justify-between">
            <span>Pan Y:</span>
            <span>{Math.round(canvasState.panY)}px</span>
          </div>
          {canvasState.selectedRegion && (
            <div className="flex justify-between">
              <span>Selected:</span>
              <span className="text-blue-600">Region</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CanvasControls;
