'use client';

import React, { useState, useMemo } from 'react';
import { CanvasTextRegion } from '@/types/canvas';
import { TextRegionType } from '@/types/api';

interface TextRegionListProps {
  textRegions: CanvasTextRegion[];
  selectedRegionId?: string;
  onRegionSelect: (regionId: string) => void;
  onRegionUpdate: (regionId: string, updates: Partial<CanvasTextRegion>) => void;
  onRegionDelete: (regionId: string) => void;
  className?: string;
}

export const TextRegionList: React.FC<TextRegionListProps> = ({
  textRegions,
  selectedRegionId,
  onRegionSelect,
  onRegionUpdate,
  onRegionDelete,
  className = ''
}) => {
  const [sortBy, setSortBy] = useState<'position' | 'type' | 'status'>('position');
  const [filterType, setFilterType] = useState<TextRegionType | 'all'>('all');
  const [searchText, setSearchText] = useState('');

  // Filter and sort regions
  const filteredAndSortedRegions = useMemo(() => {
    let filtered = textRegions;

    // Filter by type
    if (filterType !== 'all') {
      filtered = filtered.filter(region => region.region_type === filterType);
    }

    // Filter by search text
    if (searchText) {
      const search = searchText.toLowerCase();
      filtered = filtered.filter(region => 
        (region.original_text?.toLowerCase().includes(search)) ||
        (region.translated_text?.toLowerCase().includes(search))
      );
    }

    // Sort regions
    return filtered.sort((a, b) => {
      switch (sortBy) {
        case 'position':
          // Sort by Y position first, then X position
          if (a.y !== b.y) return a.y - b.y;
          return a.x - b.x;
        case 'type':
          return a.region_type.localeCompare(b.region_type);
        case 'status':
          return a.translation_status.localeCompare(b.translation_status);
        default:
          return 0;
      }
    });
  }, [textRegions, filterType, searchText, sortBy]);

  const getRegionTypeIcon = (type: TextRegionType) => {
    switch (type) {
      case TextRegionType.SPEECH_BUBBLE:
        return '💬';
      case TextRegionType.THOUGHT_BUBBLE:
        return '💭';
      case TextRegionType.NARRATION:
        return '📖';
      case TextRegionType.SOUND_EFFECT:
        return '💥';
      case TextRegionType.SIGN:
        return '🪧';
      default:
        return '📝';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleQuickEdit = (regionId: string, field: string, value: any) => {
    onRegionUpdate(regionId, { [field]: value });
  };

  const handleDelete = (regionId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    if (confirm('Are you sure you want to delete this text region?')) {
      onRegionDelete(regionId);
    }
  };

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-medium text-gray-900">
            Text Regions ({filteredAndSortedRegions.length})
          </h3>
        </div>

        {/* Search */}
        <div className="mb-3">
          <input
            type="text"
            placeholder="Search text regions..."
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Filters and Sort */}
        <div className="flex space-x-2">
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value as any)}
            className="flex-1 px-3 py-1.5 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Types</option>
            <option value={TextRegionType.SPEECH_BUBBLE}>Speech Bubble</option>
            <option value={TextRegionType.THOUGHT_BUBBLE}>Thought Bubble</option>
            <option value={TextRegionType.NARRATION}>Narration</option>
            <option value={TextRegionType.SOUND_EFFECT}>Sound Effect</option>
            <option value={TextRegionType.SIGN}>Sign</option>
            <option value={TextRegionType.OTHER}>Other</option>
          </select>

          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="flex-1 px-3 py-1.5 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="position">Sort by Position</option>
            <option value="type">Sort by Type</option>
            <option value="status">Sort by Status</option>
          </select>
        </div>
      </div>

      {/* Region List */}
      <div className="max-h-96 overflow-y-auto">
        {filteredAndSortedRegions.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            <svg className="w-12 h-12 mx-auto mb-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <p className="text-sm">No text regions found</p>
            <p className="text-xs text-gray-400 mt-1">
              {searchText || filterType !== 'all' 
                ? 'Try adjusting your filters'
                : 'Create text regions by drawing on the canvas'
              }
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredAndSortedRegions.map((region, index) => (
              <div
                key={region.id}
                onClick={() => onRegionSelect(region.id)}
                className={`p-3 cursor-pointer transition-colors hover:bg-gray-50 ${
                  selectedRegionId === region.id ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    {/* Header */}
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-lg">{getRegionTypeIcon(region.region_type)}</span>
                      <span className="text-xs font-medium text-gray-500">#{index + 1}</span>
                      <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${getStatusColor(region.translation_status)}`}>
                        {region.translation_status.replace('_', ' ')}
                      </span>
                      {region.confidence_score !== undefined && (
                        <span className="text-xs text-gray-500">
                          {Math.round(region.confidence_score * 100)}%
                        </span>
                      )}
                    </div>

                    {/* Text Content */}
                    <div className="space-y-1">
                      {region.original_text && (
                        <div>
                          <div className="text-xs text-gray-500 mb-0.5">Original:</div>
                          <div className="text-sm text-gray-900 line-clamp-2">
                            {region.original_text}
                          </div>
                        </div>
                      )}
                      {region.translated_text && (
                        <div>
                          <div className="text-xs text-gray-500 mb-0.5">Translation:</div>
                          <div className="text-sm text-gray-900 line-clamp-2">
                            {region.translated_text}
                          </div>
                        </div>
                      )}
                      {!region.original_text && !region.translated_text && (
                        <div className="text-sm text-gray-400 italic">
                          No text content
                        </div>
                      )}
                    </div>

                    {/* Position Info */}
                    <div className="mt-2 text-xs text-gray-500">
                      Position: ({Math.round(region.x * 100)}%, {Math.round(region.y * 100)}%) 
                      Size: {Math.round(region.width * 100)}% × {Math.round(region.height * 100)}%
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-1 ml-2">
                    <button
                      onClick={(e) => handleDelete(region.id, e)}
                      className="p-1 text-red-600 hover:text-red-700 hover:bg-red-50 rounded"
                      title="Delete region"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </div>

                {/* Quick Edit for Translation Status */}
                {selectedRegionId === region.id && (
                  <div className="mt-3 pt-3 border-t border-gray-200">
                    <div className="flex items-center space-x-2">
                      <label className="text-xs font-medium text-gray-700">Quick Status:</label>
                      <select
                        value={region.translation_status}
                        onChange={(e) => handleQuickEdit(region.id, 'translation_status', e.target.value)}
                        onClick={(e) => e.stopPropagation()}
                        className="text-xs px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                      >
                        <option value="pending">Pending</option>
                        <option value="in_progress">In Progress</option>
                        <option value="completed">Completed</option>
                        <option value="failed">Failed</option>
                      </select>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default TextRegionList;
