'use client';

import React, { useRef, useEffect, useCallback } from 'react';
import { CanvasState, CanvasImageInfo } from '@/types/canvas';

interface CanvasGridRulersProps {
  canvasState: CanvasState;
  imageInfo: CanvasImageInfo | null;
  showGrid: boolean;
  showRulers: boolean;
  gridSize?: number;
  rulerSize?: number;
  className?: string;
}

export const CanvasGridRulers: React.FC<CanvasGridRulersProps> = ({
  canvasState,
  imageInfo,
  showGrid,
  showRulers,
  gridSize = 20,
  rulerSize = 20,
  className = ''
}) => {
  const gridCanvasRef = useRef<HTMLCanvasElement>(null);
  const horizontalRulerRef = useRef<HTMLCanvasElement>(null);
  const verticalRulerRef = useRef<HTMLCanvasElement>(null);

  // Draw grid
  const drawGrid = useCallback(() => {
    if (!showGrid || !imageInfo) return;

    const canvas = gridCanvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Calculate grid spacing based on zoom
    const spacing = gridSize * canvasState.zoom;
    
    // Only draw grid if spacing is reasonable
    if (spacing < 5) return;

    // Calculate visible area
    const startX = Math.floor(-canvasState.panX / spacing) * spacing + canvasState.panX;
    const startY = Math.floor(-canvasState.panY / spacing) * spacing + canvasState.panY;

    // Set grid style
    ctx.strokeStyle = '#e5e7eb';
    ctx.lineWidth = 1;
    ctx.globalAlpha = 0.5;

    // Draw vertical lines
    for (let x = startX; x < canvas.width; x += spacing) {
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, canvas.height);
      ctx.stroke();
    }

    // Draw horizontal lines
    for (let y = startY; y < canvas.height; y += spacing) {
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(canvas.width, y);
      ctx.stroke();
    }

    ctx.globalAlpha = 1;
  }, [showGrid, imageInfo, canvasState, gridSize]);

  // Draw horizontal ruler
  const drawHorizontalRuler = useCallback(() => {
    if (!showRulers || !imageInfo) return;

    const canvas = horizontalRulerRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Set ruler style
    ctx.fillStyle = '#f9fafb';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Draw border
    ctx.strokeStyle = '#d1d5db';
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.moveTo(0, canvas.height - 0.5);
    ctx.lineTo(canvas.width, canvas.height - 0.5);
    ctx.stroke();

    // Calculate tick spacing
    const zoom = canvasState.zoom;
    let tickSpacing = 50; // Base spacing in pixels
    let tickValue = 50; // Value per tick

    // Adjust spacing based on zoom
    if (zoom < 0.5) {
      tickSpacing = 100;
      tickValue = 100;
    } else if (zoom > 2) {
      tickSpacing = 25;
      tickValue = 25;
    }

    const actualSpacing = tickSpacing * zoom;

    // Only draw if spacing is reasonable
    if (actualSpacing < 10) return;

    // Calculate starting position
    const startX = Math.floor(-canvasState.panX / actualSpacing) * actualSpacing + canvasState.panX;
    const startValue = Math.floor(-canvasState.panX / actualSpacing) * tickValue;

    // Set text style
    ctx.fillStyle = '#374151';
    ctx.font = '10px sans-serif';
    ctx.textAlign = 'center';

    // Draw ticks and labels
    for (let x = startX, value = startValue; x < canvas.width; x += actualSpacing, value += tickValue) {
      // Major tick
      ctx.strokeStyle = '#6b7280';
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.moveTo(x, canvas.height - 8);
      ctx.lineTo(x, canvas.height);
      ctx.stroke();

      // Label
      if (value >= 0) {
        ctx.fillText(value.toString(), x, canvas.height - 10);
      }

      // Minor ticks
      const minorSpacing = actualSpacing / 5;
      if (minorSpacing >= 5) {
        ctx.strokeStyle = '#9ca3af';
        ctx.lineWidth = 1;
        for (let i = 1; i < 5; i++) {
          const minorX = x + i * minorSpacing;
          if (minorX < canvas.width) {
            ctx.beginPath();
            ctx.moveTo(minorX, canvas.height - 4);
            ctx.lineTo(minorX, canvas.height);
            ctx.stroke();
          }
        }
      }
    }
  }, [showRulers, imageInfo, canvasState]);

  // Draw vertical ruler
  const drawVerticalRuler = useCallback(() => {
    if (!showRulers || !imageInfo) return;

    const canvas = verticalRulerRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Set ruler style
    ctx.fillStyle = '#f9fafb';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Draw border
    ctx.strokeStyle = '#d1d5db';
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.moveTo(canvas.width - 0.5, 0);
    ctx.lineTo(canvas.width - 0.5, canvas.height);
    ctx.stroke();

    // Calculate tick spacing
    const zoom = canvasState.zoom;
    let tickSpacing = 50; // Base spacing in pixels
    let tickValue = 50; // Value per tick

    // Adjust spacing based on zoom
    if (zoom < 0.5) {
      tickSpacing = 100;
      tickValue = 100;
    } else if (zoom > 2) {
      tickSpacing = 25;
      tickValue = 25;
    }

    const actualSpacing = tickSpacing * zoom;

    // Only draw if spacing is reasonable
    if (actualSpacing < 10) return;

    // Calculate starting position
    const startY = Math.floor(-canvasState.panY / actualSpacing) * actualSpacing + canvasState.panY;
    const startValue = Math.floor(-canvasState.panY / actualSpacing) * tickValue;

    // Set text style
    ctx.fillStyle = '#374151';
    ctx.font = '10px sans-serif';
    ctx.textAlign = 'center';

    // Draw ticks and labels
    for (let y = startY, value = startValue; y < canvas.height; y += actualSpacing, value += tickValue) {
      // Major tick
      ctx.strokeStyle = '#6b7280';
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.moveTo(canvas.width - 8, y);
      ctx.lineTo(canvas.width, y);
      ctx.stroke();

      // Label (rotated)
      if (value >= 0) {
        ctx.save();
        ctx.translate(canvas.width - 12, y);
        ctx.rotate(-Math.PI / 2);
        ctx.fillText(value.toString(), 0, 0);
        ctx.restore();
      }

      // Minor ticks
      const minorSpacing = actualSpacing / 5;
      if (minorSpacing >= 5) {
        ctx.strokeStyle = '#9ca3af';
        ctx.lineWidth = 1;
        for (let i = 1; i < 5; i++) {
          const minorY = y + i * minorSpacing;
          if (minorY < canvas.height) {
            ctx.beginPath();
            ctx.moveTo(canvas.width - 4, minorY);
            ctx.lineTo(canvas.width, minorY);
            ctx.stroke();
          }
        }
      }
    }
  }, [showRulers, imageInfo, canvasState]);

  // Redraw when dependencies change
  useEffect(() => {
    drawGrid();
  }, [drawGrid]);

  useEffect(() => {
    drawHorizontalRuler();
  }, [drawHorizontalRuler]);

  useEffect(() => {
    drawVerticalRuler();
  }, [drawVerticalRuler]);

  // Handle canvas resize
  useEffect(() => {
    const resizeCanvases = () => {
      if (gridCanvasRef.current) {
        const parent = gridCanvasRef.current.parentElement;
        if (parent) {
          gridCanvasRef.current.width = parent.clientWidth;
          gridCanvasRef.current.height = parent.clientHeight;
        }
      }

      if (horizontalRulerRef.current) {
        const parent = horizontalRulerRef.current.parentElement;
        if (parent) {
          horizontalRulerRef.current.width = parent.clientWidth;
          horizontalRulerRef.current.height = rulerSize;
        }
      }

      if (verticalRulerRef.current) {
        const parent = verticalRulerRef.current.parentElement;
        if (parent) {
          verticalRulerRef.current.width = rulerSize;
          verticalRulerRef.current.height = parent.clientHeight;
        }
      }
    };

    resizeCanvases();
    window.addEventListener('resize', resizeCanvases);
    return () => window.removeEventListener('resize', resizeCanvases);
  }, [rulerSize]);

  return (
    <div className={`absolute inset-0 pointer-events-none ${className}`}>
      {/* Grid Canvas */}
      {showGrid && (
        <canvas
          ref={gridCanvasRef}
          className="absolute inset-0 w-full h-full"
          style={{ zIndex: 1 }}
        />
      )}

      {/* Horizontal Ruler */}
      {showRulers && (
        <canvas
          ref={horizontalRulerRef}
          className="absolute top-0 left-0 w-full"
          style={{ 
            height: rulerSize,
            zIndex: 10,
            marginLeft: showRulers ? rulerSize : 0
          }}
        />
      )}

      {/* Vertical Ruler */}
      {showRulers && (
        <canvas
          ref={verticalRulerRef}
          className="absolute top-0 left-0 h-full"
          style={{ 
            width: rulerSize,
            zIndex: 10,
            marginTop: showRulers ? rulerSize : 0
          }}
        />
      )}

      {/* Ruler Corner */}
      {showRulers && (
        <div
          className="absolute top-0 left-0 bg-gray-50 border-r border-b border-gray-300"
          style={{
            width: rulerSize,
            height: rulerSize,
            zIndex: 11
          }}
        >
          <div className="w-full h-full flex items-center justify-center">
            <svg className="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
            </svg>
          </div>
        </div>
      )}
    </div>
  );
};

export default CanvasGridRulers;
