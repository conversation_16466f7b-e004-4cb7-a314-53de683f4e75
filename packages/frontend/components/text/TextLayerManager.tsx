'use client';

import React, { useState } from 'react';
import { CanvasTextRegion } from '@/types/canvas';

interface TextLayerManagerProps {
  textRegions: CanvasTextRegion[];
  selectedRegionId?: string;
  onRegionSelect: (regionId: string) => void;
  onRegionUpdate: (regionId: string, updates: Partial<CanvasTextRegion>) => void;
  onRegionDelete: (regionId: string) => void;
  onRegionDuplicate: (regionId: string) => void;
  onLayerReorder: (regionId: string, direction: 'up' | 'down') => void;
  className?: string;
}

export const TextLayerManager: React.FC<TextLayerManagerProps> = ({
  textRegions,
  selectedRegionId,
  onRegionSelect,
  onRegionUpdate,
  onRegionDelete,
  onRegionDuplicate,
  onLayerReorder,
  className = ''
}) => {
  const [showVisibilityControls, setShowVisibilityControls] = useState(false);
  const [draggedRegion, setDraggedRegion] = useState<string | null>(null);

  // Sort regions by z-index or creation order
  const sortedRegions = [...textRegions].sort((a, b) => {
    // If we had z-index, we'd sort by that
    // For now, sort by creation time (newest first)
    return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
  });

  // Toggle region visibility
  const toggleVisibility = (regionId: string) => {
    const region = textRegions.find(r => r.id === regionId);
    if (region) {
      // Note: visibility would need to be added to CanvasTextRegion type
      // For now, we'll use opacity as a proxy
      const isVisible = region.fillOpacity !== 0;
      onRegionUpdate(regionId, { 
        fillOpacity: isVisible ? 0 : 0.2,
        // visibility: !isVisible 
      });
    }
  };

  // Lock/unlock region
  const toggleLock = (regionId: string) => {
    const region = textRegions.find(r => r.id === regionId);
    if (region) {
      // Note: locked would need to be added to CanvasTextRegion type
      // onRegionUpdate(regionId, { locked: !region.locked });
      console.log(`Toggle lock for region ${regionId}`);
    }
  };

  // Handle drag start
  const handleDragStart = (e: React.DragEvent, regionId: string) => {
    setDraggedRegion(regionId);
    e.dataTransfer.effectAllowed = 'move';
  };

  // Handle drag over
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  // Handle drop
  const handleDrop = (e: React.DragEvent, targetRegionId: string) => {
    e.preventDefault();
    if (draggedRegion && draggedRegion !== targetRegionId) {
      // Reorder layers
      console.log(`Reorder ${draggedRegion} relative to ${targetRegionId}`);
      // Implementation would depend on how layers are managed
    }
    setDraggedRegion(null);
  };

  const getRegionTypeIcon = (type: string) => {
    switch (type) {
      case 'speech_bubble':
        return '💬';
      case 'thought_bubble':
        return '💭';
      case 'narration':
        return '📖';
      case 'sound_effect':
        return '💥';
      case 'sign':
        return '🪧';
      default:
        return '📝';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600';
      case 'in_progress':
        return 'text-blue-600';
      case 'failed':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      {/* Header */}
      <div className="p-3 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium text-gray-900">
            Text Layers ({textRegions.length})
          </h3>
          <div className="flex items-center space-x-1">
            <button
              onClick={() => setShowVisibilityControls(!showVisibilityControls)}
              className={`p-1 rounded text-xs ${
                showVisibilityControls ? 'bg-blue-100 text-blue-700' : 'text-gray-400 hover:text-gray-600'
              }`}
              title="Toggle visibility controls"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Layer List */}
      <div className="max-h-64 overflow-y-auto">
        {sortedRegions.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            <svg className="w-8 h-8 mx-auto mb-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM7 21h16M7 21l4-4M7 21l-4-4" />
            </svg>
            <p className="text-xs">No text layers</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {sortedRegions.map((region, index) => {
              const isSelected = selectedRegionId === region.id;
              const isVisible = region.fillOpacity !== 0;
              
              return (
                <div
                  key={region.id}
                  draggable
                  onDragStart={(e) => handleDragStart(e, region.id)}
                  onDragOver={handleDragOver}
                  onDrop={(e) => handleDrop(e, region.id)}
                  onClick={() => onRegionSelect(region.id)}
                  className={`p-2 cursor-pointer transition-colors ${
                    isSelected ? 'bg-blue-50 border-l-2 border-blue-500' : 'hover:bg-gray-50'
                  } ${draggedRegion === region.id ? 'opacity-50' : ''}`}
                >
                  <div className="flex items-center space-x-2">
                    {/* Drag Handle */}
                    <div className="cursor-move text-gray-400">
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8h16M4 16h16" />
                      </svg>
                    </div>

                    {/* Layer Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm">{getRegionTypeIcon(region.region_type)}</span>
                        <span className="text-xs font-medium text-gray-900">
                          Layer {sortedRegions.length - index}
                        </span>
                        <span className={`text-xs ${getStatusColor(region.translation_status)}`}>
                          ●
                        </span>
                      </div>
                      
                      <div className="text-xs text-gray-600 truncate mt-1">
                        {region.translated_text || region.original_text || 'Empty text'}
                      </div>
                      
                      <div className="text-xs text-gray-500 mt-1">
                        {Math.round(region.x * 100)}%, {Math.round(region.y * 100)}%
                      </div>
                    </div>

                    {/* Controls */}
                    <div className="flex items-center space-x-1">
                      {/* Visibility Toggle */}
                      {showVisibilityControls && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleVisibility(region.id);
                          }}
                          className={`p-1 rounded ${
                            isVisible ? 'text-gray-600 hover:text-gray-800' : 'text-gray-300 hover:text-gray-500'
                          }`}
                          title={isVisible ? 'Hide layer' : 'Show layer'}
                        >
                          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            {isVisible ? (
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            ) : (
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                            )}
                          </svg>
                        </button>
                      )}

                      {/* Layer Actions */}
                      <div className="flex items-center space-x-1">
                        {/* Move Up */}
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            onLayerReorder(region.id, 'up');
                          }}
                          disabled={index === 0}
                          className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-30 disabled:cursor-not-allowed"
                          title="Move layer up"
                        >
                          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                          </svg>
                        </button>

                        {/* Move Down */}
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            onLayerReorder(region.id, 'down');
                          }}
                          disabled={index === sortedRegions.length - 1}
                          className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-30 disabled:cursor-not-allowed"
                          title="Move layer down"
                        >
                          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </svg>
                        </button>

                        {/* Duplicate */}
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            onRegionDuplicate(region.id);
                          }}
                          className="p-1 text-gray-400 hover:text-blue-600"
                          title="Duplicate layer"
                        >
                          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
                          </svg>
                        </button>

                        {/* Delete */}
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            if (confirm('Delete this text layer?')) {
                              onRegionDelete(region.id);
                            }
                          }}
                          className="p-1 text-gray-400 hover:text-red-600"
                          title="Delete layer"
                        >
                          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Layer Actions */}
      <div className="p-3 border-t border-gray-200">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>Drag to reorder layers</span>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => {
                // Select all layers
                console.log('Select all layers');
              }}
              className="text-blue-600 hover:text-blue-700"
            >
              Select All
            </button>
            <span>•</span>
            <button
              onClick={() => {
                // Hide all layers
                textRegions.forEach(region => {
                  onRegionUpdate(region.id, { fillOpacity: 0 });
                });
              }}
              className="text-gray-600 hover:text-gray-700"
            >
              Hide All
            </button>
            <span>•</span>
            <button
              onClick={() => {
                // Show all layers
                textRegions.forEach(region => {
                  onRegionUpdate(region.id, { fillOpacity: 0.2 });
                });
              }}
              className="text-gray-600 hover:text-gray-700"
            >
              Show All
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TextLayerManager;
