'use client';

import React, { useState, useEffect } from 'react';
import { CanvasTextRegion, FontConfig, DEFAULT_FONT_CONFIG } from '@/types/canvas';

interface FontCustomizerProps {
  region: CanvasTextRegion | null;
  onUpdate: (regionId: string, updates: Partial<CanvasTextRegion>) => void;
  className?: string;
}

export const FontCustomizer: React.FC<FontCustomizerProps> = ({
  region,
  onUpdate,
  className = ''
}) => {
  const [fontConfig, setFontConfig] = useState<FontConfig>(DEFAULT_FONT_CONFIG);
  const [previewText, setPreviewText] = useState('Sample Text');

  // Update font config when region changes
  useEffect(() => {
    if (region) {
      setFontConfig({
        family: region.font_family || DEFAULT_FONT_CONFIG.family,
        size: region.font_size || DEFAULT_FONT_CONFIG.size,
        color: region.font_color || DEFAULT_FONT_CONFIG.color,
        backgroundColor: region.background_color || DEFAULT_FONT_CONFIG.backgroundColor,
        weight: 'normal', // TODO: Add font weight to region type
        style: 'normal', // TODO: Add font style to region type
        align: DEFAULT_FONT_CONFIG.align
      });
      setPreviewText(region.translated_text || region.original_text || 'Sample Text');
    }
  }, [region]);

  // Font families available
  const fontFamilies = [
    { name: 'Arial', value: 'Arial, sans-serif' },
    { name: 'Helvetica', value: 'Helvetica, sans-serif' },
    { name: 'Times New Roman', value: 'Times New Roman, serif' },
    { name: 'Georgia', value: 'Georgia, serif' },
    { name: 'Verdana', value: 'Verdana, sans-serif' },
    { name: 'Tahoma', value: 'Tahoma, sans-serif' },
    { name: 'Comic Sans MS', value: 'Comic Sans MS, cursive' },
    { name: 'Impact', value: 'Impact, sans-serif' },
    { name: 'Trebuchet MS', value: 'Trebuchet MS, sans-serif' },
    { name: 'Courier New', value: 'Courier New, monospace' },
    { name: 'Roboto', value: 'Roboto, sans-serif' },
    { name: 'Open Sans', value: 'Open Sans, sans-serif' },
    { name: 'Lato', value: 'Lato, sans-serif' },
    { name: 'Montserrat', value: 'Montserrat, sans-serif' },
    { name: 'Source Sans Pro', value: 'Source Sans Pro, sans-serif' }
  ];

  // Predefined font sizes
  const fontSizes = [8, 10, 12, 14, 16, 18, 20, 24, 28, 32, 36, 42, 48, 56, 64, 72];

  // Color presets
  const colorPresets = [
    '#000000', '#FFFFFF', '#FF0000', '#00FF00', '#0000FF',
    '#FFFF00', '#FF00FF', '#00FFFF', '#FFA500', '#800080',
    '#FFC0CB', '#A52A2A', '#808080', '#000080', '#008000'
  ];

  // Background color presets
  const backgroundPresets = [
    'transparent', '#FFFFFF', '#000000', '#F0F0F0', '#E0E0E0',
    '#FFE4E1', '#E0FFFF', '#F0FFF0', '#FFF8DC', '#F5F5DC'
  ];

  // Handle font config changes
  const handleFontChange = (field: keyof FontConfig, value: any) => {
    const newConfig = { ...fontConfig, [field]: value };
    setFontConfig(newConfig);

    if (region) {
      const updates: Partial<CanvasTextRegion> = {};
      
      switch (field) {
        case 'family':
          updates.font_family = value;
          break;
        case 'size':
          updates.font_size = value;
          break;
        case 'color':
          updates.font_color = value;
          break;
        case 'backgroundColor':
          updates.background_color = value;
          break;
      }

      onUpdate(region.id, updates);
    }
  };

  // Apply preset style
  const applyPreset = (preset: 'manga' | 'comic' | 'novel' | 'subtitle') => {
    let presetConfig: Partial<FontConfig>;

    switch (preset) {
      case 'manga':
        presetConfig = {
          family: 'Arial, sans-serif',
          size: 14,
          color: '#000000',
          backgroundColor: 'transparent',
          weight: 'bold',
          align: 'center'
        };
        break;
      case 'comic':
        presetConfig = {
          family: 'Comic Sans MS, cursive',
          size: 16,
          color: '#000000',
          backgroundColor: '#FFFFFF',
          weight: 'bold',
          align: 'center'
        };
        break;
      case 'novel':
        presetConfig = {
          family: 'Times New Roman, serif',
          size: 12,
          color: '#000000',
          backgroundColor: 'transparent',
          weight: 'normal',
          align: 'left'
        };
        break;
      case 'subtitle':
        presetConfig = {
          family: 'Arial, sans-serif',
          size: 18,
          color: '#FFFFFF',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          weight: 'bold',
          align: 'center'
        };
        break;
    }

    const newConfig = { ...fontConfig, ...presetConfig };
    setFontConfig(newConfig);

    if (region) {
      onUpdate(region.id, {
        font_family: newConfig.family,
        font_size: newConfig.size,
        font_color: newConfig.color,
        background_color: newConfig.backgroundColor
      });
    }
  };

  if (!region) {
    return (
      <div className={`bg-white border border-gray-200 rounded-lg shadow-sm p-4 ${className}`}>
        <div className="text-center text-gray-500">
          <svg className="w-12 h-12 mx-auto mb-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
          </svg>
          <p className="text-sm">Select a text region to customize fonts</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900">Font Customizer</h3>
      </div>

      <div className="p-4 space-y-4">
        {/* Preview */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Preview
          </label>
          <div className="border border-gray-300 rounded-lg p-4 bg-gray-50 min-h-[80px] flex items-center justify-center">
            <div
              style={{
                fontFamily: fontConfig.family,
                fontSize: `${fontConfig.size}px`,
                color: fontConfig.color,
                backgroundColor: fontConfig.backgroundColor === 'transparent' ? 'transparent' : fontConfig.backgroundColor,
                fontWeight: fontConfig.weight,
                fontStyle: fontConfig.style,
                textAlign: fontConfig.align,
                padding: fontConfig.backgroundColor !== 'transparent' ? '4px 8px' : '0',
                borderRadius: fontConfig.backgroundColor !== 'transparent' ? '4px' : '0'
              }}
            >
              {previewText}
            </div>
          </div>
          <input
            type="text"
            value={previewText}
            onChange={(e) => setPreviewText(e.target.value)}
            className="mt-2 w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter preview text..."
          />
        </div>

        {/* Style Presets */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Style Presets
          </label>
          <div className="grid grid-cols-2 gap-2">
            {[
              { id: 'manga', name: 'Manga', icon: '📖' },
              { id: 'comic', name: 'Comic', icon: '💭' },
              { id: 'novel', name: 'Novel', icon: '📚' },
              { id: 'subtitle', name: 'Subtitle', icon: '🎬' }
            ].map((preset) => (
              <button
                key={preset.id}
                onClick={() => applyPreset(preset.id as any)}
                className="flex items-center space-x-2 p-2 border border-gray-200 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <span className="text-lg">{preset.icon}</span>
                <span className="text-sm font-medium">{preset.name}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Font Family */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Font Family
          </label>
          <select
            value={fontConfig.family}
            onChange={(e) => handleFontChange('family', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            {fontFamilies.map((font) => (
              <option key={font.value} value={font.value} style={{ fontFamily: font.value }}>
                {font.name}
              </option>
            ))}
          </select>
        </div>

        {/* Font Size */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Font Size
          </label>
          <div className="flex items-center space-x-2">
            <input
              type="range"
              min="8"
              max="72"
              value={fontConfig.size}
              onChange={(e) => handleFontChange('size', parseInt(e.target.value))}
              className="flex-1"
            />
            <input
              type="number"
              min="8"
              max="72"
              value={fontConfig.size}
              onChange={(e) => handleFontChange('size', parseInt(e.target.value))}
              className="w-16 px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <span className="text-sm text-gray-500">px</span>
          </div>
          <div className="flex flex-wrap gap-1 mt-2">
            {fontSizes.map((size) => (
              <button
                key={size}
                onClick={() => handleFontChange('size', size)}
                className={`px-2 py-1 text-xs border rounded ${
                  fontConfig.size === size
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-200 hover:bg-gray-50'
                }`}
              >
                {size}
              </button>
            ))}
          </div>
        </div>

        {/* Font Weight & Style */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Weight
            </label>
            <select
              value={fontConfig.weight}
              onChange={(e) => handleFontChange('weight', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="normal">Normal</option>
              <option value="bold">Bold</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Style
            </label>
            <select
              value={fontConfig.style}
              onChange={(e) => handleFontChange('style', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="normal">Normal</option>
              <option value="italic">Italic</option>
            </select>
          </div>
        </div>

        {/* Text Alignment */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Text Alignment
          </label>
          <div className="flex space-x-1">
            {[
              { value: 'left', icon: '⬅️', label: 'Left' },
              { value: 'center', icon: '↔️', label: 'Center' },
              { value: 'right', icon: '➡️', label: 'Right' }
            ].map((align) => (
              <button
                key={align.value}
                onClick={() => handleFontChange('align', align.value)}
                className={`flex-1 p-2 border rounded-md text-sm font-medium transition-colors ${
                  fontConfig.align === align.value
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-200 hover:bg-gray-50'
                }`}
                title={align.label}
              >
                <span className="block text-center">{align.icon}</span>
                <span className="block text-xs mt-1">{align.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Font Color */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Font Color
          </label>
          <div className="flex items-center space-x-2 mb-2">
            <input
              type="color"
              value={fontConfig.color}
              onChange={(e) => handleFontChange('color', e.target.value)}
              className="w-12 h-8 border border-gray-300 rounded cursor-pointer"
            />
            <input
              type="text"
              value={fontConfig.color}
              onChange={(e) => handleFontChange('color', e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="#000000"
            />
          </div>
          <div className="flex flex-wrap gap-1">
            {colorPresets.map((color) => (
              <button
                key={color}
                onClick={() => handleFontChange('color', color)}
                className={`w-6 h-6 rounded border-2 ${
                  fontConfig.color === color ? 'border-blue-500' : 'border-gray-300'
                }`}
                style={{ backgroundColor: color }}
                title={color}
              />
            ))}
          </div>
        </div>

        {/* Background Color */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Background Color
          </label>
          <div className="flex items-center space-x-2 mb-2">
            <input
              type="color"
              value={fontConfig.backgroundColor === 'transparent' ? '#ffffff' : fontConfig.backgroundColor}
              onChange={(e) => handleFontChange('backgroundColor', e.target.value)}
              className="w-12 h-8 border border-gray-300 rounded cursor-pointer"
            />
            <input
              type="text"
              value={fontConfig.backgroundColor}
              onChange={(e) => handleFontChange('backgroundColor', e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="transparent or #ffffff"
            />
          </div>
          <div className="flex flex-wrap gap-1">
            {backgroundPresets.map((color) => (
              <button
                key={color}
                onClick={() => handleFontChange('backgroundColor', color)}
                className={`w-6 h-6 rounded border-2 ${
                  fontConfig.backgroundColor === color ? 'border-blue-500' : 'border-gray-300'
                } ${color === 'transparent' ? 'bg-white' : ''}`}
                style={{ 
                  backgroundColor: color === 'transparent' ? 'transparent' : color,
                  backgroundImage: color === 'transparent' ? 'linear-gradient(45deg, #ccc 25%, transparent 25%), linear-gradient(-45deg, #ccc 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #ccc 75%), linear-gradient(-45deg, transparent 75%, #ccc 75%)' : 'none',
                  backgroundSize: color === 'transparent' ? '8px 8px' : 'auto',
                  backgroundPosition: color === 'transparent' ? '0 0, 0 4px, 4px -4px, -4px 0px' : 'auto'
                }}
                title={color}
              />
            ))}
          </div>
        </div>

        {/* Reset Button */}
        <div className="pt-4 border-t border-gray-200">
          <button
            onClick={() => {
              setFontConfig(DEFAULT_FONT_CONFIG);
              if (region) {
                onUpdate(region.id, {
                  font_family: DEFAULT_FONT_CONFIG.family,
                  font_size: DEFAULT_FONT_CONFIG.size,
                  font_color: DEFAULT_FONT_CONFIG.color,
                  background_color: DEFAULT_FONT_CONFIG.backgroundColor
                });
              }
            }}
            className="w-full px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
          >
            Reset to Default
          </button>
        </div>
      </div>
    </div>
  );
};

export default FontCustomizer;
