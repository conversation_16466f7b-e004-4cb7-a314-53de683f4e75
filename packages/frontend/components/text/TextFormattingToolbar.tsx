'use client';

import React, { useState } from 'react';
import { CanvasTextRegion } from '@/types/canvas';

interface TextFormattingToolbarProps {
  region: CanvasTextRegion | null;
  onUpdate: (regionId: string, updates: Partial<CanvasTextRegion>) => void;
  onCopy?: (region: CanvasTextRegion) => void;
  onPaste?: (regionId: string) => void;
  onDuplicate?: (region: CanvasTextRegion) => void;
  className?: string;
}

export const TextFormattingToolbar: React.FC<TextFormattingToolbarProps> = ({
  region,
  onUpdate,
  onCopy,
  onPaste,
  onDuplicate,
  className = ''
}) => {
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [activeColorType, setActiveColorType] = useState<'text' | 'background'>('text');

  // Quick font size adjustments
  const adjustFontSize = (delta: number) => {
    if (!region) return;
    const currentSize = region.font_size || 14;
    const newSize = Math.max(8, Math.min(72, currentSize + delta));
    onUpdate(region.id, { font_size: newSize });
  };

  // Toggle text formatting
  const toggleFormat = (format: 'bold' | 'italic' | 'underline') => {
    if (!region) return;
    // Note: These would need to be added to the CanvasTextRegion type
    // For now, we'll handle them as part of font styling
    console.log(`Toggle ${format} for region ${region.id}`);
  };

  // Quick color changes
  const quickColors = [
    { name: 'Black', value: '#000000' },
    { name: 'White', value: '#FFFFFF' },
    { name: 'Red', value: '#FF0000' },
    { name: 'Blue', value: '#0000FF' },
    { name: 'Green', value: '#008000' },
    { name: 'Yellow', value: '#FFFF00' }
  ];

  // Text alignment options
  const alignmentOptions = [
    { value: 'left', icon: '⬅️', label: 'Left' },
    { value: 'center', icon: '↔️', label: 'Center' },
    { value: 'right', icon: '➡️', label: 'Right' }
  ];

  if (!region) {
    return (
      <div className={`bg-gray-100 border border-gray-200 rounded-lg p-2 ${className}`}>
        <div className="text-center text-gray-500 text-sm">
          Select a text region to access formatting tools
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      <div className="p-2">
        <div className="flex flex-wrap items-center gap-1">
          {/* Font Size Controls */}
          <div className="flex items-center border border-gray-200 rounded">
            <button
              onClick={() => adjustFontSize(-2)}
              className="p-1 hover:bg-gray-100 rounded-l"
              title="Decrease font size"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
              </svg>
            </button>
            <span className="px-2 py-1 text-sm font-medium border-x border-gray-200 min-w-[40px] text-center">
              {region.font_size || 14}
            </span>
            <button
              onClick={() => adjustFontSize(2)}
              className="p-1 hover:bg-gray-100 rounded-r"
              title="Increase font size"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
            </button>
          </div>

          {/* Separator */}
          <div className="w-px h-6 bg-gray-300"></div>

          {/* Text Formatting */}
          <div className="flex items-center space-x-1">
            <button
              onClick={() => toggleFormat('bold')}
              className="p-1 hover:bg-gray-100 rounded text-sm font-bold"
              title="Bold"
            >
              B
            </button>
            <button
              onClick={() => toggleFormat('italic')}
              className="p-1 hover:bg-gray-100 rounded text-sm italic"
              title="Italic"
            >
              I
            </button>
            <button
              onClick={() => toggleFormat('underline')}
              className="p-1 hover:bg-gray-100 rounded text-sm underline"
              title="Underline"
            >
              U
            </button>
          </div>

          {/* Separator */}
          <div className="w-px h-6 bg-gray-300"></div>

          {/* Text Alignment */}
          <div className="flex items-center border border-gray-200 rounded">
            {alignmentOptions.map((align) => (
              <button
                key={align.value}
                onClick={() => onUpdate(region.id, { /* text_align: align.value */ })}
                className={`p-1 hover:bg-gray-100 first:rounded-l last:rounded-r ${
                  /* region.text_align === align.value ? 'bg-blue-100' : '' */ ''
                }`}
                title={align.label}
              >
                <span className="text-xs">{align.icon}</span>
              </button>
            ))}
          </div>

          {/* Separator */}
          <div className="w-px h-6 bg-gray-300"></div>

          {/* Color Controls */}
          <div className="flex items-center space-x-1">
            <div className="relative">
              <button
                onClick={() => {
                  setActiveColorType('text');
                  setShowColorPicker(!showColorPicker);
                }}
                className="flex items-center p-1 hover:bg-gray-100 rounded"
                title="Text color"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM7 21h16M7 21l4-4M7 21l-4-4" />
                </svg>
                <div
                  className="w-3 h-1 ml-1 rounded"
                  style={{ backgroundColor: region.font_color || '#000000' }}
                ></div>
              </button>
            </div>

            <div className="relative">
              <button
                onClick={() => {
                  setActiveColorType('background');
                  setShowColorPicker(!showColorPicker);
                }}
                className="flex items-center p-1 hover:bg-gray-100 rounded"
                title="Background color"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM7 21h16M7 21l4-4M7 21l-4-4" />
                </svg>
                <div
                  className="w-3 h-1 ml-1 rounded border border-gray-300"
                  style={{ 
                    backgroundColor: region.background_color === 'transparent' ? 'transparent' : region.background_color || 'transparent',
                    backgroundImage: region.background_color === 'transparent' ? 'linear-gradient(45deg, #ccc 25%, transparent 25%), linear-gradient(-45deg, #ccc 25%, transparent 25%)' : 'none',
                    backgroundSize: region.background_color === 'transparent' ? '4px 4px' : 'auto'
                  }}
                ></div>
              </button>
            </div>
          </div>

          {/* Separator */}
          <div className="w-px h-6 bg-gray-300"></div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-1">
            {onCopy && (
              <button
                onClick={() => onCopy(region)}
                className="p-1 hover:bg-gray-100 rounded"
                title="Copy formatting"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
              </button>
            )}

            {onPaste && (
              <button
                onClick={() => onPaste(region.id)}
                className="p-1 hover:bg-gray-100 rounded"
                title="Paste formatting"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </button>
            )}

            {onDuplicate && (
              <button
                onClick={() => onDuplicate(region)}
                className="p-1 hover:bg-gray-100 rounded"
                title="Duplicate region"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
                </svg>
              </button>
            )}
          </div>
        </div>

        {/* Color Picker Dropdown */}
        {showColorPicker && (
          <div className="absolute z-10 mt-2 p-3 bg-white border border-gray-200 rounded-lg shadow-lg">
            <div className="mb-2">
              <h4 className="text-sm font-medium text-gray-900 mb-2">
                {activeColorType === 'text' ? 'Text Color' : 'Background Color'}
              </h4>
              <div className="flex items-center space-x-2 mb-2">
                <input
                  type="color"
                  value={
                    activeColorType === 'text' 
                      ? region.font_color || '#000000'
                      : region.background_color === 'transparent' ? '#ffffff' : region.background_color || '#ffffff'
                  }
                  onChange={(e) => {
                    const field = activeColorType === 'text' ? 'font_color' : 'background_color';
                    onUpdate(region.id, { [field]: e.target.value });
                  }}
                  className="w-8 h-8 border border-gray-300 rounded cursor-pointer"
                />
                <input
                  type="text"
                  value={
                    activeColorType === 'text' 
                      ? region.font_color || '#000000'
                      : region.background_color || 'transparent'
                  }
                  onChange={(e) => {
                    const field = activeColorType === 'text' ? 'font_color' : 'background_color';
                    onUpdate(region.id, { [field]: e.target.value });
                  }}
                  className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder={activeColorType === 'text' ? '#000000' : 'transparent'}
                />
              </div>
              
              {/* Quick Colors */}
              <div className="grid grid-cols-6 gap-1">
                {quickColors.map((color) => (
                  <button
                    key={color.value}
                    onClick={() => {
                      const field = activeColorType === 'text' ? 'font_color' : 'background_color';
                      onUpdate(region.id, { [field]: color.value });
                    }}
                    className="w-6 h-6 rounded border border-gray-300 hover:scale-110 transition-transform"
                    style={{ backgroundColor: color.value }}
                    title={color.name}
                  />
                ))}
                
                {/* Transparent option for background */}
                {activeColorType === 'background' && (
                  <button
                    onClick={() => onUpdate(region.id, { background_color: 'transparent' })}
                    className="w-6 h-6 rounded border border-gray-300 hover:scale-110 transition-transform bg-white"
                    style={{
                      backgroundImage: 'linear-gradient(45deg, #ccc 25%, transparent 25%), linear-gradient(-45deg, #ccc 25%, transparent 25%)',
                      backgroundSize: '4px 4px'
                    }}
                    title="Transparent"
                  />
                )}
              </div>
            </div>
            
            <button
              onClick={() => setShowColorPicker(false)}
              className="w-full mt-2 px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded"
            >
              Close
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default TextFormattingToolbar;
