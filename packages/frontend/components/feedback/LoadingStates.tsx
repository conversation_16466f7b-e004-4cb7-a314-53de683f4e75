'use client';

import React from 'react';

// Basic spinner component
export const Spinner: React.FC<{ size?: 'sm' | 'md' | 'lg'; className?: string }> = ({
  size = 'md',
  className = ''
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };

  return (
    <div className={`animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 ${sizeClasses[size]} ${className}`} />
  );
};

// Loading overlay for full screen
export const LoadingOverlay: React.FC<{
  message?: string;
  isVisible: boolean;
  className?: string;
}> = ({
  message = 'Loading...',
  isVisible,
  className = ''
}) => {
    if (!isVisible) return null;

    return (
      <div className={`fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 ${className}`}>
        <div className="bg-white rounded-lg p-6 max-w-sm w-full mx-4">
          <div className="flex items-center space-x-3">
            <Spinner size="lg" />
            <div>
              <h3 className="text-lg font-medium text-gray-900">{message}</h3>
              <p className="text-sm text-gray-600">Please wait...</p>
            </div>
          </div>
        </div>
      </div>
    );
  };

// Loading skeleton for content
export const LoadingSkeleton: React.FC<{
  lines?: number;
  className?: string;
}> = ({
  lines = 3,
  className = ''
}) => {
    return (
      <div className={`animate-pulse ${className}`}>
        {Array.from({ length: lines }).map((_, index) => (
          <div
            key={index}
            className={`bg-gray-200 rounded h-4 mb-2 ${index === lines - 1 ? 'w-3/4' : 'w-full'
              }`}
          />
        ))}
      </div>
    );
  };

// Loading card skeleton
export const LoadingCard: React.FC<{ className?: string }> = ({ className = '' }) => {
  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-4 ${className}`}>
      <div className="animate-pulse">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
          <div className="flex-1">
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-1/4"></div>
          </div>
        </div>
        <div className="space-y-2">
          <div className="h-3 bg-gray-200 rounded"></div>
          <div className="h-3 bg-gray-200 rounded w-5/6"></div>
          <div className="h-3 bg-gray-200 rounded w-4/6"></div>
        </div>
      </div>
    </div>
  );
};

// Progress bar component
export const ProgressBar: React.FC<{
  progress: number;
  label?: string;
  showPercentage?: boolean;
  className?: string;
}> = ({
  progress,
  label,
  showPercentage = true,
  className = ''
}) => {
    const clampedProgress = Math.max(0, Math.min(100, progress));

    return (
      <div className={className}>
        {(label || showPercentage) && (
          <div className="flex justify-between items-center mb-2">
            {label && <span className="text-sm font-medium text-gray-700">{label}</span>}
            {showPercentage && (
              <span className="text-sm text-gray-600">{Math.round(clampedProgress)}%</span>
            )}
          </div>
        )}
        <div className="bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
            style={{ width: `${clampedProgress}%` }}
          />
        </div>
      </div>
    );
  };

// Loading button state
export const LoadingButton: React.FC<{
  isLoading: boolean;
  children: React.ReactNode;
  disabled?: boolean;
  onClick?: () => void;
  className?: string;
  loadingText?: string;
}> = ({
  isLoading,
  children,
  disabled = false,
  onClick,
  className = '',
  loadingText
}) => {
    return (
      <button
        onClick={onClick}
        disabled={disabled || isLoading}
        className={`relative flex items-center justify-center px-4 py-2 rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-1 disabled:opacity-50 disabled:cursor-not-allowed ${className}`}
      >
        {isLoading && (
          <Spinner size="sm" className="mr-2" />
        )}
        {isLoading && loadingText ? loadingText : children}
      </button>
    );
  };

// Loading state for lists
export const LoadingList: React.FC<{
  itemCount?: number;
  className?: string;
}> = ({
  itemCount = 5,
  className = ''
}) => {
    return (
      <div className={`space-y-3 ${className}`}>
        {Array.from({ length: itemCount }).map((_, index) => (
          <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
            <div className="animate-pulse flex items-center space-x-3 w-full">
              <div className="w-8 h-8 bg-gray-200 rounded"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
              <div className="w-6 h-6 bg-gray-200 rounded"></div>
            </div>
          </div>
        ))}
      </div>
    );
  };

// Loading state for canvas
export const CanvasLoading: React.FC<{
  message?: string;
  className?: string;
}> = ({
  message = 'Loading canvas...',
  className = ''
}) => {
    return (
      <div className={`flex items-center justify-center bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg ${className}`}>
        <div className="text-center p-8">
          <Spinner size="lg" className="mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">{message}</h3>
          <p className="text-sm text-gray-600">Please wait while we prepare your workspace</p>
        </div>
      </div>
    );
  };

// Loading state for image
export const ImageLoading: React.FC<{
  width?: number;
  height?: number;
  className?: string;
}> = ({
  width = 200,
  height = 150,
  className = ''
}) => {
    return (
      <div
        className={`flex items-center justify-center bg-gray-100 border border-gray-200 rounded ${className}`}
        style={{ width, height }}
      >
        <div className="text-center">
          <svg className="w-8 h-8 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          <Spinner size="sm" className="mx-auto" />
        </div>
      </div>
    );
  };

// Loading dots animation
export const LoadingDots: React.FC<{ className?: string }> = ({ className = '' }) => {
  return (
    <div className={`flex space-x-1 ${className}`}>
      {[0, 1, 2].map((index) => (
        <div
          key={index}
          className="w-2 h-2 bg-blue-600 rounded-full animate-pulse"
          style={{
            animationDelay: `${index * 0.2}s`,
            animationDuration: '1s'
          }}
        />
      ))}
    </div>
  );
};

// Pulse loading effect
export const PulseLoading: React.FC<{
  children: React.ReactNode;
  isLoading: boolean;
  className?: string;
}> = ({
  children,
  isLoading,
  className = ''
}) => {
    return (
      <div className={`${isLoading ? 'animate-pulse opacity-50' : ''} ${className}`}>
        {children}
      </div>
    );
  };

export default {
  Spinner,
  LoadingOverlay,
  LoadingSkeleton,
  LoadingCard,
  ProgressBar,
  LoadingButton,
  LoadingList,
  CanvasLoading,
  ImageLoading,
  LoadingDots,
  PulseLoading
};
