'use client';

import React, { useState } from 'react';

// Error severity levels
export type ErrorSeverity = 'info' | 'warning' | 'error' | 'critical';

// Error display props
interface ErrorDisplayProps {
  title?: string;
  message: string;
  severity?: ErrorSeverity;
  details?: string;
  onRetry?: () => void;
  onDismiss?: () => void;
  showDetails?: boolean;
  className?: string;
}

// Get severity styles
const getSeverityStyles = (severity: ErrorSeverity) => {
  switch (severity) {
    case 'info':
      return {
        container: 'bg-blue-50 border-blue-200',
        icon: 'text-blue-600',
        title: 'text-blue-800',
        message: 'text-blue-700',
        button: 'bg-blue-600 hover:bg-blue-700 text-white'
      };
    case 'warning':
      return {
        container: 'bg-yellow-50 border-yellow-200',
        icon: 'text-yellow-600',
        title: 'text-yellow-800',
        message: 'text-yellow-700',
        button: 'bg-yellow-600 hover:bg-yellow-700 text-white'
      };
    case 'error':
      return {
        container: 'bg-red-50 border-red-200',
        icon: 'text-red-600',
        title: 'text-red-800',
        message: 'text-red-700',
        button: 'bg-red-600 hover:bg-red-700 text-white'
      };
    case 'critical':
      return {
        container: 'bg-red-100 border-red-300',
        icon: 'text-red-700',
        title: 'text-red-900',
        message: 'text-red-800',
        button: 'bg-red-700 hover:bg-red-800 text-white'
      };
  }
};

// Get severity icon
const getSeverityIcon = (severity: ErrorSeverity) => {
  switch (severity) {
    case 'info':
      return (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      );
    case 'warning':
      return (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      );
    case 'error':
    case 'critical':
      return (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      );
  }
};

// Main error display component
export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  title,
  message,
  severity = 'error',
  details,
  onRetry,
  onDismiss,
  showDetails = false,
  className = ''
}) => {
  const [isDetailsExpanded, setIsDetailsExpanded] = useState(showDetails);
  const styles = getSeverityStyles(severity);

  return (
    <div className={`border rounded-lg p-4 ${styles.container} ${className}`}>
      <div className="flex items-start">
        <div className={`flex-shrink-0 ${styles.icon}`}>
          {getSeverityIcon(severity)}
        </div>
        
        <div className="ml-3 flex-1">
          {title && (
            <h3 className={`text-sm font-medium ${styles.title} mb-1`}>
              {title}
            </h3>
          )}
          
          <p className={`text-sm ${styles.message}`}>
            {message}
          </p>

          {details && (
            <div className="mt-2">
              <button
                onClick={() => setIsDetailsExpanded(!isDetailsExpanded)}
                className={`text-xs ${styles.message} hover:underline focus:outline-none`}
              >
                {isDetailsExpanded ? 'Hide Details' : 'Show Details'}
              </button>
              
              {isDetailsExpanded && (
                <div className={`mt-2 p-2 bg-white bg-opacity-50 rounded text-xs ${styles.message} font-mono whitespace-pre-wrap`}>
                  {details}
                </div>
              )}
            </div>
          )}

          {(onRetry || onDismiss) && (
            <div className="mt-3 flex space-x-2">
              {onRetry && (
                <button
                  onClick={onRetry}
                  className={`px-3 py-1 text-xs rounded-md font-medium ${styles.button} focus:outline-none focus:ring-2 focus:ring-offset-1`}
                >
                  Try Again
                </button>
              )}
              {onDismiss && (
                <button
                  onClick={onDismiss}
                  className={`px-3 py-1 text-xs rounded-md font-medium border ${styles.message} hover:bg-white hover:bg-opacity-50 focus:outline-none focus:ring-2 focus:ring-offset-1`}
                >
                  Dismiss
                </button>
              )}
            </div>
          )}
        </div>

        {onDismiss && (
          <div className="ml-4">
            <button
              onClick={onDismiss}
              className={`${styles.icon} hover:opacity-75 focus:outline-none`}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

// Inline error component for form fields
export const InlineError: React.FC<{
  message: string;
  className?: string;
}> = ({ message, className = '' }) => {
  return (
    <div className={`flex items-center mt-1 text-sm text-red-600 ${className}`}>
      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      <span>{message}</span>
    </div>
  );
};

// Empty state component
export const EmptyState: React.FC<{
  title: string;
  description?: string;
  icon?: React.ReactNode;
  action?: {
    label: string;
    onClick: () => void;
  };
  className?: string;
}> = ({
  title,
  description,
  icon,
  action,
  className = ''
}) => {
  const defaultIcon = (
    <svg className="w-12 h-12 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
    </svg>
  );

  return (
    <div className={`text-center py-12 ${className}`}>
      <div className="mx-auto mb-4">
        {icon || defaultIcon}
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
      {description && (
        <p className="text-gray-600 mb-4 max-w-sm mx-auto">{description}</p>
      )}
      {action && (
        <button
          onClick={action.onClick}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
        >
          {action.label}
        </button>
      )}
    </div>
  );
};

// Network error component
export const NetworkError: React.FC<{
  onRetry?: () => void;
  className?: string;
}> = ({ onRetry, className = '' }) => {
  return (
    <ErrorDisplay
      title="Connection Error"
      message="Unable to connect to the server. Please check your internet connection and try again."
      severity="error"
      onRetry={onRetry}
      className={className}
    />
  );
};

// Not found error component
export const NotFoundError: React.FC<{
  resource?: string;
  onGoBack?: () => void;
  className?: string;
}> = ({ resource = 'page', onGoBack, className = '' }) => {
  return (
    <div className={`text-center py-12 ${className}`}>
      <div className="mx-auto mb-4">
        <svg className="w-16 h-16 text-gray-300 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      </div>
      <h1 className="text-4xl font-bold text-gray-900 mb-2">404</h1>
      <h2 className="text-xl font-medium text-gray-700 mb-4">
        {resource.charAt(0).toUpperCase() + resource.slice(1)} Not Found
      </h2>
      <p className="text-gray-600 mb-6 max-w-sm mx-auto">
        The {resource} you're looking for doesn't exist or has been moved.
      </p>
      {onGoBack && (
        <button
          onClick={onGoBack}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
        >
          Go Back
        </button>
      )}
    </div>
  );
};

export default {
  ErrorDisplay,
  InlineError,
  EmptyState,
  NetworkError,
  NotFoundError
};
