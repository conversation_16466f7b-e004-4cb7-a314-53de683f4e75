'use client';

import React, { useState, useEffect } from 'react';
import {
  LLMProvider,
  OCRJobResponse,
  OCRJobDetailResponse,
  OCRProcessRequest,
  LLMProvidersStatus,
  TextRegionType,
  TranslationStatus
} from '@/types/api';
import { ocrAPI, llmProvidersAPI } from '@/lib/api-client';
import { CanvasTextRegion } from '@/types/canvas';

interface OCRProcessorProps {
  pageId: string;
  onOCRComplete: (regions: CanvasTextRegion[]) => void;
  onClose: () => void;
  className?: string;
}

export const OCRProcessor: React.FC<OCRProcessorProps> = ({
  pageId,
  onOCRComplete,
  onClose,
  className = ''
}) => {
  const [providers, setProviders] = useState<LLMProvidersStatus | null>(null);
  const [selectedProvider, setSelectedProvider] = useState<LLMProvider>(LLMProvider.CLAUDE);
  const [customPrompt, setCustomPrompt] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentJob, setCurrentJob] = useState<OCRJobResponse | null>(null);
  const [jobResults, setJobResults] = useState<OCRJobDetailResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);

  // Load available providers
  useEffect(() => {
    const loadProviders = async () => {
      try {
        const providersStatus = await llmProvidersAPI.getProvidersStatus();
        setProviders(providersStatus);

        // Set default provider to the first available one
        const availableProvider = providersStatus.providers.find(p => p.is_available);
        if (availableProvider) {
          setSelectedProvider(availableProvider.provider);
        }
      } catch (err) {
        setError('Failed to load OCR providers');
      }
    };

    loadProviders();
  }, []);

  // Poll job status
  useEffect(() => {
    if (!currentJob || currentJob.status === 'completed' || currentJob.status === 'failed') {
      return;
    }

    const pollInterval = setInterval(async () => {
      try {
        const updatedJob = await ocrAPI.getOCRJob(currentJob.id);
        setCurrentJob(updatedJob);

        // Update progress based on status
        if (updatedJob.status === 'processing') {
          setProgress(prev => Math.min(prev + 10, 90));
        } else if (updatedJob.status === 'completed') {
          setProgress(100);
          // Load detailed results
          const detailedResults = await ocrAPI.getOCRJobDetail(updatedJob.id);
          setJobResults(detailedResults);
          clearInterval(pollInterval);
        } else if (updatedJob.status === 'failed') {
          setError(updatedJob.error_message || 'OCR processing failed');
          clearInterval(pollInterval);
        }
      } catch (err) {
        setError('Failed to check job status');
        clearInterval(pollInterval);
      }
    }, 2000);

    return () => clearInterval(pollInterval);
  }, [currentJob]);

  // Start OCR processing
  const handleStartOCR = async () => {
    try {
      setIsProcessing(true);
      setError(null);
      setProgress(10);

      const request: OCRProcessRequest = {
        page_id: pageId,
        provider: selectedProvider,
        custom_prompt: customPrompt.trim() || undefined
      };

      const job = await ocrAPI.processOCR(request);
      setCurrentJob(job);
      setProgress(20);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start OCR processing');
      setIsProcessing(false);
      setProgress(0);
    }
  };

  // Apply OCR results to canvas
  const handleApplyResults = () => {
    if (!jobResults) return;

    const regions: CanvasTextRegion[] = jobResults.results.map((result, index) => ({
      id: `ocr-region-${result.id}`,
      page_id: pageId,
      region_type: result.region_type || TextRegionType.SPEECH_BUBBLE,
      x: result.x || 0,
      y: result.y || 0,
      width: result.width || 0.1,
      height: result.height || 0.1,
      original_text: result.detected_text,
      confidence_score: result.confidence_score,
      translated_text: undefined,
      translation_status: TranslationStatus.PENDING,
      font_family: 'Arial',
      font_size: 14,
      font_color: '#000000',
      background_color: 'transparent',
      isSelected: false,
      isEditing: false,
      borderColor: '#3b82f6',
      fillOpacity: 0.2,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }));

    onOCRComplete(regions);
    onClose();
  };

  // Retry failed job
  const handleRetry = async () => {
    if (!currentJob) return;

    try {
      setError(null);
      setProgress(10);
      const retriedJob = await ocrAPI.retryOCRJob(currentJob.id);
      setCurrentJob(retriedJob);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to retry OCR job');
    }
  };

  const getProviderIcon = (provider: LLMProvider) => {
    switch (provider) {
      case LLMProvider.CLAUDE:
        return '🤖';
      case LLMProvider.OPENAI:
        return '🧠';
      case LLMProvider.GEMINI:
        return '💎';
      case LLMProvider.DEEPSEEK:
        return '🔍';
      default:
        return '🤖';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600';
      case 'processing':
        return 'text-blue-600';
      case 'failed':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">OCR Processing</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      <div className="p-4 space-y-4">
        {/* Provider Selection */}
        {!isProcessing && !currentJob && (
          <>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                OCR Provider
              </label>
              <div className="grid grid-cols-1 gap-2">
                {providers?.providers.map((provider) => (
                  <label
                    key={provider.provider}
                    className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${selectedProvider === provider.provider
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                      } ${!provider.is_available ? 'opacity-50 cursor-not-allowed' : ''}`}
                  >
                    <input
                      type="radio"
                      name="provider"
                      value={provider.provider}
                      checked={selectedProvider === provider.provider}
                      onChange={(e) => setSelectedProvider(e.target.value as LLMProvider)}
                      disabled={!provider.is_available}
                      className="sr-only"
                    />
                    <div className="flex items-center space-x-3 flex-1">
                      <span className="text-2xl">{getProviderIcon(provider.provider)}</span>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium text-gray-900 capitalize">
                            {provider.provider}
                          </span>
                          {!provider.is_available && (
                            <span className="px-2 py-0.5 bg-red-100 text-red-800 text-xs rounded-full">
                              Unavailable
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-600">
                          {provider.supports_vision ? 'Vision-enabled' : 'Text-only'} •
                          {provider.available_models.length} models available
                        </p>
                      </div>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {/* Custom Prompt */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Custom Prompt (Optional)
              </label>
              <textarea
                value={customPrompt}
                onChange={(e) => setCustomPrompt(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter custom instructions for OCR processing..."
              />
              <p className="text-xs text-gray-500 mt-1">
                Leave empty to use default OCR prompt for manga text detection
              </p>
            </div>

            {/* Start Button */}
            <button
              onClick={handleStartOCR}
              disabled={!providers?.providers.some(p => p.is_available)}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Start OCR Processing
            </button>
          </>
        )}

        {/* Processing Status */}
        {(isProcessing || currentJob) && (
          <div className="space-y-4">
            {/* Job Info */}
            {currentJob && (
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900">Job Status</h4>
                  <span className={`font-medium capitalize ${getStatusColor(currentJob.status)}`}>
                    {currentJob.status.replace('_', ' ')}
                  </span>
                </div>
                <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                  <div>
                    <span className="font-medium">Provider:</span> {currentJob.provider}
                  </div>
                  <div>
                    <span className="font-medium">Job ID:</span> {currentJob.id.slice(0, 8)}...
                  </div>
                  {currentJob.processing_time && (
                    <div>
                      <span className="font-medium">Processing Time:</span> {currentJob.processing_time}s
                    </div>
                  )}
                  {currentJob.result_count > 0 && (
                    <div>
                      <span className="font-medium">Results:</span> {currentJob.result_count} regions
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Progress Bar */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">Progress</span>
                <span className="text-sm text-gray-600">{progress}%</span>
              </div>
              <div className="bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
            </div>

            {/* Processing Steps */}
            <div className="space-y-2">
              <div className={`flex items-center space-x-2 ${progress >= 20 ? 'text-green-600' : 'text-gray-400'}`}>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span className="text-sm">Job submitted</span>
              </div>
              <div className={`flex items-center space-x-2 ${progress >= 50 ? 'text-green-600' : 'text-gray-400'}`}>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span className="text-sm">Analyzing image</span>
              </div>
              <div className={`flex items-center space-x-2 ${progress >= 80 ? 'text-green-600' : 'text-gray-400'}`}>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span className="text-sm">Extracting text regions</span>
              </div>
              <div className={`flex items-center space-x-2 ${progress >= 100 ? 'text-green-600' : 'text-gray-400'}`}>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span className="text-sm">Processing complete</span>
              </div>
            </div>
          </div>
        )}

        {/* Results */}
        {jobResults && (
          <div className="space-y-4">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center">
                <svg className="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <h4 className="font-medium text-green-800">OCR Complete!</h4>
              </div>
              <p className="text-sm text-green-700 mt-1">
                Found {jobResults.results.length} text regions with an average confidence of{' '}
                {Math.round((jobResults.results.reduce((sum, r) => sum + (r.confidence_score || 0), 0) / jobResults.results.length) * 100)}%
              </p>
            </div>

            {/* Results Preview */}
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Detected Text Regions</h4>
              <div className="max-h-48 overflow-y-auto space-y-2">
                {jobResults.results.map((result, index) => (
                  <div key={result.id} className="bg-gray-50 rounded p-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">
                          Region {index + 1}
                        </p>
                        <p className="text-sm text-gray-600 mt-1">
                          {result.detected_text}
                        </p>
                      </div>
                      {result.confidence_score && (
                        <span className="text-xs text-gray-500">
                          {Math.round(result.confidence_score * 100)}%
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Apply Results Button */}
            <button
              onClick={handleApplyResults}
              className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-1"
            >
              Apply Results to Canvas
            </button>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <svg className="w-5 h-5 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h4 className="font-medium text-red-800">Error</h4>
              </div>
              {currentJob && (
                <button
                  onClick={handleRetry}
                  className="text-sm text-red-700 hover:text-red-800 underline"
                >
                  Retry
                </button>
              )}
            </div>
            <p className="text-sm text-red-700 mt-1">{error}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default OCRProcessor;
