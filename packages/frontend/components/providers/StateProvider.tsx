'use client';

import React, { useEffect, ReactNode } from 'react';
import { AppProvider, useAppContext } from '@/store/AppContext';
import { persistenceUtils } from '@/store/persistence';
import ErrorBoundary from '@/components/feedback/ErrorBoundary';
import { ToastProvider } from '@/components/feedback/ToastNotification';
import { ErrorProvider } from '@/components/providers/ErrorProvider';

// State initialization component
const StateInitializer: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { state, dispatch } = useAppContext();

  useEffect(() => {
    // Initialize state from persistence on app start
    const initializeState = async () => {
      try {
        // Load user preferences
        const preferences = persistenceUtils.loadPreferences();
        if (preferences) {
          dispatch({ type: 'UPDATE_PREFERENCES', payload: preferences });
        }

        // Load UI state
        const uiState = persistenceUtils.loadUIState();
        if (uiState) {
          // Apply UI state
          Object.entries(uiState).forEach(([key, value]) => {
            if (typeof value === 'boolean') {
              dispatch({
                type: 'SET_PANEL_VISIBILITY',
                payload: { panel: key as any, visible: value }
              });
            } else if (key === 'sidebarWidth') {
              dispatch({ type: 'SET_SIDEBAR_WIDTH', payload: value as number });
            } else if (key === 'rightPanelWidth') {
              dispatch({ type: 'SET_RIGHT_PANEL_WIDTH', payload: value as number });
            }
          });
        }

        // Load canvas state
        const canvasState = persistenceUtils.loadCanvasState();
        if (canvasState) {
          dispatch({ type: 'UPDATE_CANVAS_STATE', payload: canvasState });
        }

        // Load session data
        const sessionData = persistenceUtils.loadSessionData();
        if (sessionData) {
          // Restore session-specific data like current project/page
          if (sessionData.currentProjectId) {
            // This would need to be implemented with API calls
            console.log('Restoring session for project:', sessionData.currentProjectId);
          }
        }

        console.log('State initialized from persistence');
      } catch (error) {
        console.error('Error initializing state from persistence:', error);
      }
    };

    initializeState();
  }, [dispatch]);

  // Auto-save state changes
  useEffect(() => {
    const saveState = () => {
      try {
        // Save preferences
        persistenceUtils.savePreferences(state.preferences);

        // Save UI state
        persistenceUtils.saveUIState(state.ui);

        // Save canvas state
        persistenceUtils.saveCanvasState(state.canvas);

        // Save session data
        const sessionData = {
          currentProjectId: state.currentProject?.id,
          currentPageId: state.currentPage?.id,
          lastActivity: Date.now()
        };
        persistenceUtils.saveSessionData(sessionData);

        // Save text regions for current page
        if (state.currentPage) {
          persistenceUtils.saveTextRegions(state.currentPage.id, state.textRegions);
        }
      } catch (error) {
        console.error('Error saving state to persistence:', error);
      }
    };

    // Debounce saves to avoid excessive writes
    const timeoutId = setTimeout(saveState, 1000);
    return () => clearTimeout(timeoutId);
  }, [state.preferences, state.ui, state.canvas, state.currentProject, state.currentPage, state.textRegions]);

  // Handle page visibility changes for auto-save
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden') {
        // Force save when page becomes hidden
        try {
          persistenceUtils.savePreferences(state.preferences);
          persistenceUtils.saveUIState(state.ui);
          persistenceUtils.saveCanvasState(state.canvas);

          if (state.currentPage) {
            persistenceUtils.saveTextRegions(state.currentPage.id, state.textRegions);
          }
        } catch (error) {
          console.error('Error saving state on visibility change:', error);
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [state]);

  // Handle beforeunload for final save
  useEffect(() => {
    const handleBeforeUnload = () => {
      try {
        // Force synchronous save before page unload
        persistenceUtils.savePreferences(state.preferences);
        persistenceUtils.saveUIState(state.ui);
        persistenceUtils.saveCanvasState(state.canvas);

        if (state.currentPage) {
          persistenceUtils.saveTextRegions(state.currentPage.id, state.textRegions);
        }
      } catch (error) {
        console.error('Error saving state before unload:', error);
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [state]);

  return <>{children}</>;
};

// Storage quota warning component
const StorageQuotaWarning: React.FC = () => {
  const [showWarning, setShowWarning] = React.useState(false);
  const [storageInfo, setStorageInfo] = React.useState({ used: 0, available: 0, percentage: 0 });

  useEffect(() => {
    const checkStorageQuota = () => {
      const info = persistenceUtils.getStorageInfo();
      setStorageInfo(info);

      // Show warning if storage is over 80% full
      if (info.percentage > 80) {
        setShowWarning(true);
      }
    };

    // Check on mount and periodically
    checkStorageQuota();
    const interval = setInterval(checkStorageQuota, 60000); // Check every minute

    return () => clearInterval(interval);
  }, []);

  const handleClearCache = () => {
    persistenceUtils.clearCache();
    setShowWarning(false);
    // Recheck storage
    const info = persistenceUtils.getStorageInfo();
    setStorageInfo(info);
  };

  if (!showWarning) return null;

  return (
    <div className="fixed bottom-4 right-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-lg z-50 max-w-sm">
      <div className="flex items-start">
        <svg className="w-5 h-5 text-yellow-600 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
        <div className="flex-1">
          <h4 className="text-sm font-medium text-yellow-800">Storage Almost Full</h4>
          <p className="text-sm text-yellow-700 mt-1">
            Your browser storage is {Math.round(storageInfo.percentage)}% full.
            Consider clearing cache to free up space.
          </p>
          <div className="mt-3 flex space-x-2">
            <button
              onClick={handleClearCache}
              className="text-xs bg-yellow-600 text-white px-3 py-1 rounded hover:bg-yellow-700"
            >
              Clear Cache
            </button>
            <button
              onClick={() => setShowWarning(false)}
              className="text-xs text-yellow-600 hover:text-yellow-700"
            >
              Dismiss
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Main state provider component
export const StateProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        console.error('State provider error:', error, errorInfo);
        // In production, you might want to send this to an error reporting service
      }}
    >
      <ToastProvider>
        <ErrorProvider>
          <AppProvider>
            <StateInitializer>
              {children}
              <StorageQuotaWarning />
            </StateInitializer>
          </AppProvider>
        </ErrorProvider>
      </ToastProvider>
    </ErrorBoundary>
  );
};

// Hook for accessing state management utilities
export const useStateManagement = () => {
  const { state, dispatch } = useAppContext();

  const exportAppData = () => {
    try {
      const data = persistenceUtils.exportData();
      const blob = new Blob([data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = `ho-trans-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      return true;
    } catch (error) {
      console.error('Error exporting app data:', error);
      return false;
    }
  };

  const importAppData = (file: File): Promise<boolean> => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = e.target?.result as string;
          const success = persistenceUtils.importData(data);
          if (success) {
            // Reload the page to apply imported data
            window.location.reload();
          }
          resolve(success);
        } catch (error) {
          console.error('Error importing app data:', error);
          resolve(false);
        }
      };
      reader.onerror = () => resolve(false);
      reader.readAsText(file);
    });
  };

  const clearAllData = () => {
    if (confirm('Are you sure you want to clear all app data? This cannot be undone.')) {
      persistenceUtils.clearAllData();
      window.location.reload();
      return true;
    }
    return false;
  };

  const getStorageInfo = () => {
    return persistenceUtils.getStorageInfo();
  };

  return {
    state,
    dispatch,
    exportAppData,
    importAppData,
    clearAllData,
    getStorageInfo
  };
};

export default StateProvider;
