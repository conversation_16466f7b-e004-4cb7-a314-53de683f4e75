'use client';

import React, { createContext, useContext, useState, useCallback, ReactNode, useEffect } from 'react';
import { ErrorDisplay, ErrorSeverity } from '@/components/feedback/ErrorDisplay';
import { useToastHelpers } from '@/components/feedback/ToastNotification';

// Global error interface
interface GlobalError {
  id: string;
  title?: string;
  message: string;
  severity: ErrorSeverity;
  details?: string;
  timestamp: Date;
  source?: string;
  retryAction?: () => void;
  dismissible?: boolean;
}

// Error context interface
interface ErrorContextType {
  errors: GlobalError[];
  addError: (error: Omit<GlobalError, 'id' | 'timestamp'>) => void;
  removeError: (id: string) => void;
  clearErrors: () => void;
  hasErrors: boolean;
  hasCriticalErrors: boolean;
}

// Error context
const ErrorContext = createContext<ErrorContextType | null>(null);

// Error hook
export const useErrorHandler = () => {
  const context = useContext(ErrorContext);
  if (!context) {
    throw new Error('useErrorHandler must be used within an ErrorProvider');
  }
  return context;
};

// Global error display component
const GlobalErrorDisplay: React.FC<{
  errors: GlobalError[];
  onRemove: (id: string) => void;
}> = ({ errors, onRemove }) => {
  if (errors.length === 0) return null;

  // Show only critical errors in global display
  const criticalErrors = errors.filter(error => error.severity === 'critical');
  
  if (criticalErrors.length === 0) return null;

  return (
    <div className="fixed top-0 left-0 right-0 z-50 bg-red-600 text-white p-4">
      <div className="max-w-7xl mx-auto">
        {criticalErrors.map((error) => (
          <div key={error.id} className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div>
                {error.title && (
                  <h3 className="font-medium">{error.title}</h3>
                )}
                <p className="text-sm">{error.message}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {error.retryAction && (
                <button
                  onClick={error.retryAction}
                  className="px-3 py-1 bg-white bg-opacity-20 rounded text-sm hover:bg-opacity-30 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
                >
                  Retry
                </button>
              )}
              {error.dismissible !== false && (
                <button
                  onClick={() => onRemove(error.id)}
                  className="p-1 hover:bg-white hover:bg-opacity-20 rounded focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Error provider component
export const ErrorProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [errors, setErrors] = useState<GlobalError[]>([]);
  const toast = useToastHelpers();

  const addError = useCallback((error: Omit<GlobalError, 'id' | 'timestamp'>) => {
    const id = Math.random().toString(36).substr(2, 9);
    const newError: GlobalError = {
      ...error,
      id,
      timestamp: new Date(),
      dismissible: error.dismissible ?? true
    };

    setErrors(prev => [...prev, newError]);

    // Show toast for non-critical errors
    if (error.severity !== 'critical') {
      switch (error.severity) {
        case 'error':
          toast.error(error.message, { title: error.title });
          break;
        case 'warning':
          toast.warning(error.message, { title: error.title });
          break;
        case 'info':
          toast.info(error.message, { title: error.title });
          break;
      }
    }

    // Auto-remove non-critical errors after 10 seconds
    if (error.severity !== 'critical' && error.dismissible !== false) {
      setTimeout(() => {
        removeError(id);
      }, 10000);
    }
  }, [toast]);

  const removeError = useCallback((id: string) => {
    setErrors(prev => prev.filter(error => error.id !== id));
  }, []);

  const clearErrors = useCallback(() => {
    setErrors([]);
  }, []);

  const hasErrors = errors.length > 0;
  const hasCriticalErrors = errors.some(error => error.severity === 'critical');

  // Global error handlers
  useEffect(() => {
    // Handle unhandled promise rejections
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error('Unhandled promise rejection:', event.reason);
      addError({
        title: 'Unexpected Error',
        message: 'An unexpected error occurred. Please try refreshing the page.',
        severity: 'error',
        details: event.reason?.toString(),
        source: 'unhandled-promise'
      });
    };

    // Handle JavaScript errors
    const handleError = (event: ErrorEvent) => {
      console.error('JavaScript error:', event.error);
      addError({
        title: 'Application Error',
        message: 'A JavaScript error occurred. Please try refreshing the page.',
        severity: 'error',
        details: `${event.filename}:${event.lineno}:${event.colno} - ${event.message}`,
        source: 'javascript-error'
      });
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    window.addEventListener('error', handleError);

    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      window.removeEventListener('error', handleError);
    };
  }, [addError]);

  return (
    <ErrorContext.Provider value={{
      errors,
      addError,
      removeError,
      clearErrors,
      hasErrors,
      hasCriticalErrors
    }}>
      {children}
      <GlobalErrorDisplay errors={errors} onRemove={removeError} />
    </ErrorContext.Provider>
  );
};

// Convenience hooks for specific error types
export const useApiErrorHandler = () => {
  const { addError } = useErrorHandler();

  return useCallback((error: any, context?: string) => {
    let message = 'An API error occurred';
    let details = '';

    if (error?.response) {
      // HTTP error response
      const status = error.response.status;
      const statusText = error.response.statusText;
      message = `API Error (${status}): ${statusText}`;
      details = error.response.data?.message || error.response.data?.error || '';
    } else if (error?.message) {
      message = error.message;
      details = error.stack || '';
    } else {
      details = String(error);
    }

    addError({
      title: context ? `${context} Failed` : 'API Error',
      message,
      severity: 'error',
      details,
      source: 'api'
    });
  }, [addError]);
};

export const useNetworkErrorHandler = () => {
  const { addError } = useErrorHandler();

  return useCallback((retryAction?: () => void) => {
    addError({
      title: 'Network Error',
      message: 'Unable to connect to the server. Please check your internet connection.',
      severity: 'warning',
      source: 'network',
      retryAction
    });
  }, [addError]);
};

export const useValidationErrorHandler = () => {
  const { addError } = useErrorHandler();

  return useCallback((errors: Record<string, string[]> | string[]) => {
    if (Array.isArray(errors)) {
      errors.forEach(error => {
        addError({
          title: 'Validation Error',
          message: error,
          severity: 'warning',
          source: 'validation'
        });
      });
    } else {
      Object.entries(errors).forEach(([field, fieldErrors]) => {
        fieldErrors.forEach(error => {
          addError({
            title: `${field} Validation Error`,
            message: error,
            severity: 'warning',
            source: 'validation'
          });
        });
      });
    }
  }, [addError]);
};

// Error boundary integration
export const useErrorBoundaryHandler = () => {
  const { addError } = useErrorHandler();

  return useCallback((error: Error, errorInfo: React.ErrorInfo) => {
    addError({
      title: 'Component Error',
      message: 'A component error occurred. The application may not work correctly.',
      severity: 'critical',
      details: `${error.message}\n\nComponent Stack:\n${errorInfo.componentStack}`,
      source: 'component',
      dismissible: false,
      retryAction: () => window.location.reload()
    });
  }, [addError]);
};

export default ErrorProvider;
