'use client';

import React, { useState, useRef } from 'react';
import { useStateManagement } from '@/components/providers/StateProvider';
import { usePreferences } from '@/hooks/useAppState';

export const DataManagement: React.FC<{ className?: string }> = ({ className = '' }) => {
  const { exportAppData, importAppData, clearAllData, getStorageInfo } = useStateManagement();
  const { preferences, updatePreferences, resetPreferences } = usePreferences();
  const [storageInfo, setStorageInfo] = useState(getStorageInfo());
  const [isImporting, setIsImporting] = useState(false);
  const [importStatus, setImportStatus] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Refresh storage info
  const refreshStorageInfo = () => {
    setStorageInfo(getStorageInfo());
  };

  // Handle data export
  const handleExport = () => {
    const success = exportAppData();
    if (success) {
      setImportStatus('Data exported successfully!');
      setTimeout(() => setImportStatus(null), 3000);
    } else {
      setImportStatus('Failed to export data. Please try again.');
      setTimeout(() => setImportStatus(null), 3000);
    }
  };

  // Handle data import
  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsImporting(true);
    setImportStatus('Importing data...');

    try {
      const success = await importAppData(file);
      if (success) {
        setImportStatus('Data imported successfully! Page will reload...');
      } else {
        setImportStatus('Failed to import data. Please check the file format.');
      }
    } catch (error) {
      setImportStatus('Error importing data. Please try again.');
    } finally {
      setIsImporting(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      setTimeout(() => setImportStatus(null), 3000);
    }
  };

  // Handle clear all data
  const handleClearData = () => {
    const success = clearAllData();
    if (success) {
      setImportStatus('All data cleared. Page will reload...');
    }
  };

  // Format bytes to human readable
  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900">Data Management</h3>
        <p className="text-sm text-gray-600 mt-1">
          Manage your application data, preferences, and storage
        </p>
      </div>

      <div className="p-4 space-y-6">
        {/* Storage Information */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">Storage Usage</h4>
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-gray-600">Used Storage</span>
              <span className="text-sm font-medium text-gray-900">
                {formatBytes(storageInfo.used)} / ~5MB
              </span>
            </div>
            <div className="bg-gray-200 rounded-full h-2 mb-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${
                  storageInfo.percentage > 80 ? 'bg-red-500' : 
                  storageInfo.percentage > 60 ? 'bg-yellow-500' : 'bg-green-500'
                }`}
                style={{ width: `${Math.min(storageInfo.percentage, 100)}%` }}
              />
            </div>
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>{Math.round(storageInfo.percentage)}% used</span>
              <button
                onClick={refreshStorageInfo}
                className="text-blue-600 hover:text-blue-700"
              >
                Refresh
              </button>
            </div>
          </div>
        </div>

        {/* User Preferences */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">Preferences</h4>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Auto-save</label>
                <p className="text-xs text-gray-500">Automatically save changes</p>
              </div>
              <button
                onClick={() => updatePreferences({ autoSave: !preferences.autoSave })}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  preferences.autoSave ? 'bg-blue-600' : 'bg-gray-200'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    preferences.autoSave ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Theme</label>
                <p className="text-xs text-gray-500">Application appearance</p>
              </div>
              <select
                value={preferences.theme}
                onChange={(e) => updatePreferences({ theme: e.target.value as 'light' | 'dark' })}
                className="text-sm border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="light">Light</option>
                <option value="dark">Dark</option>
              </select>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Grid Size</label>
                <p className="text-xs text-gray-500">Canvas grid spacing</p>
              </div>
              <input
                type="number"
                min="10"
                max="50"
                value={preferences.gridSize}
                onChange={(e) => updatePreferences({ gridSize: parseInt(e.target.value) || 20 })}
                className="w-16 text-sm border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div className="pt-2">
              <button
                onClick={resetPreferences}
                className="text-sm text-red-600 hover:text-red-700"
              >
                Reset to Defaults
              </button>
            </div>
          </div>
        </div>

        {/* Data Export/Import */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">Backup & Restore</h4>
          <div className="space-y-3">
            <div>
              <button
                onClick={handleExport}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
              >
                Export Data
              </button>
              <p className="text-xs text-gray-500 mt-1">
                Download a backup of all your data and settings
              </p>
            </div>

            <div>
              <input
                ref={fileInputRef}
                type="file"
                accept=".json"
                onChange={handleImport}
                disabled={isImporting}
                className="hidden"
              />
              <button
                onClick={() => fileInputRef.current?.click()}
                disabled={isImporting}
                className="w-full px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-1 disabled:opacity-50"
              >
                {isImporting ? 'Importing...' : 'Import Data'}
              </button>
              <p className="text-xs text-gray-500 mt-1">
                Restore data from a backup file
              </p>
            </div>
          </div>
        </div>

        {/* Danger Zone */}
        <div className="border-t border-gray-200 pt-6">
          <h4 className="text-sm font-medium text-red-900 mb-3">Danger Zone</h4>
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-start">
              <svg className="w-5 h-5 text-red-600 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <div className="flex-1">
                <h5 className="text-sm font-medium text-red-800">Clear All Data</h5>
                <p className="text-sm text-red-700 mt-1">
                  This will permanently delete all your projects, settings, and cached data. 
                  This action cannot be undone.
                </p>
                <button
                  onClick={handleClearData}
                  className="mt-3 px-4 py-2 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-1"
                >
                  Clear All Data
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Status Messages */}
        {importStatus && (
          <div className={`p-3 rounded-md ${
            importStatus.includes('success') || importStatus.includes('successfully') 
              ? 'bg-green-50 border border-green-200 text-green-800'
              : importStatus.includes('Error') || importStatus.includes('Failed')
              ? 'bg-red-50 border border-red-200 text-red-800'
              : 'bg-blue-50 border border-blue-200 text-blue-800'
          }`}>
            <p className="text-sm">{importStatus}</p>
          </div>
        )}

        {/* Storage Tips */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h5 className="text-sm font-medium text-blue-900 mb-2">Storage Tips</h5>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Export your data regularly to create backups</li>
            <li>• Clear cache if storage becomes full</li>
            <li>• Large projects may use more storage space</li>
            <li>• Auto-save helps prevent data loss</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default DataManagement;
