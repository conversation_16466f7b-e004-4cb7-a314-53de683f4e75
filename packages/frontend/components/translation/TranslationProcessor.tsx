'use client';

import React, { useState, useEffect } from 'react';
import {
  LLMProvider,
  TranslationJobResponse,
  TranslationJobDetailResponse,
  TranslationProcessRequest,
  TranslationBatchProcessRequest,
  LLMProvidersStatus,
  SUPPORTED_LANGUAGES
} from '@/types/api';
import { translationAPI, llmProvidersAPI } from '@/lib/api-client';
import { CanvasTextRegion } from '@/types/canvas';

interface TranslationProcessorProps {
  textRegions: CanvasTextRegion[];
  selectedRegionIds?: string[];
  sourceLanguage: string;
  targetLanguage: string;
  onTranslationComplete: (regionId: string, translatedText: string) => void;
  onClose: () => void;
  className?: string;
}

export const TranslationProcessor: React.FC<TranslationProcessorProps> = ({
  textRegions,
  selectedRegionIds = [],
  sourceLanguage,
  targetLanguage,
  onTranslationComplete,
  onClose,
  className = ''
}) => {
  const [providers, setProviders] = useState<LLMProvidersStatus | null>(null);
  const [selectedProvider, setSelectedProvider] = useState<LLMProvider>(LLMProvider.CLAUDE);
  const [customPrompt, setCustomPrompt] = useState('');
  const [translationMode, setTranslationMode] = useState<'single' | 'batch'>('single');
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentJobs, setCurrentJobs] = useState<Map<string, TranslationJobResponse>>(new Map());
  const [completedJobs, setCompletedJobs] = useState<Map<string, TranslationJobDetailResponse>>(new Map());
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);

  // Get regions to translate
  const regionsToTranslate = selectedRegionIds.length > 0
    ? textRegions.filter(region => selectedRegionIds.includes(region.id))
    : textRegions.filter(region => region.original_text && !region.translated_text);

  // Load available providers
  useEffect(() => {
    const loadProviders = async () => {
      try {
        const providersStatus = await llmProvidersAPI.getProvidersStatus();
        setProviders(providersStatus);
        
        const availableProvider = providersStatus.providers.find(p => p.is_available);
        if (availableProvider) {
          setSelectedProvider(availableProvider.provider);
        }
      } catch (err) {
        setError('Failed to load translation providers');
      }
    };

    loadProviders();
  }, []);

  // Poll job status for active jobs
  useEffect(() => {
    const activeJobs = Array.from(currentJobs.values()).filter(
      job => job.status === 'pending' || job.status === 'in_progress'
    );

    if (activeJobs.length === 0) return;

    const pollInterval = setInterval(async () => {
      for (const job of activeJobs) {
        try {
          const updatedJob = await translationAPI.getTranslationJob(job.id);
          
          setCurrentJobs(prev => new Map(prev.set(job.text_region_id, updatedJob)));

          if (updatedJob.status === 'completed') {
            const detailedJob = await translationAPI.getTranslationJobDetail(updatedJob.id);
            setCompletedJobs(prev => new Map(prev.set(job.text_region_id, detailedJob)));
            
            // Apply the best translation result
            if (detailedJob.alternatives.length > 0) {
              const bestTranslation = detailedJob.alternatives.find(alt => alt.is_selected) 
                || detailedJob.alternatives[0];
              onTranslationComplete(job.text_region_id, bestTranslation.translated_text);
            }
          }
        } catch (err) {
          console.error('Failed to poll job status:', err);
        }
      }

      // Update progress
      const totalJobs = currentJobs.size;
      const completedJobsCount = Array.from(currentJobs.values()).filter(
        job => job.status === 'completed' || job.status === 'failed'
      ).length;
      
      if (totalJobs > 0) {
        setProgress((completedJobsCount / totalJobs) * 100);
      }

      // Stop polling if all jobs are complete
      if (completedJobsCount === totalJobs) {
        setIsProcessing(false);
        clearInterval(pollInterval);
      }
    }, 2000);

    return () => clearInterval(pollInterval);
  }, [currentJobs, onTranslationComplete]);

  // Start translation processing
  const handleStartTranslation = async () => {
    try {
      setIsProcessing(true);
      setError(null);
      setProgress(0);
      setCurrentJobs(new Map());
      setCompletedJobs(new Map());

      if (translationMode === 'batch' && regionsToTranslate.length > 1) {
        // Batch processing
        const request: TranslationBatchProcessRequest = {
          text_region_ids: regionsToTranslate.map(r => r.id),
          provider: selectedProvider,
          source_language: sourceLanguage,
          target_language: targetLanguage,
          custom_prompt: customPrompt.trim() || undefined
        };

        const jobs = await translationAPI.processBatchTranslation(request);
        const jobMap = new Map();
        jobs.forEach(job => jobMap.set(job.text_region_id, job));
        setCurrentJobs(jobMap);
      } else {
        // Single processing
        const jobMap = new Map();
        
        for (const region of regionsToTranslate) {
          if (!region.original_text) continue;
          
          const request: TranslationProcessRequest = {
            text_region_id: region.id,
            provider: selectedProvider,
            source_language: sourceLanguage,
            target_language: targetLanguage,
            original_text: region.original_text,
            custom_prompt: customPrompt.trim() || undefined
          };

          const job = await translationAPI.processTranslation(request);
          jobMap.set(region.id, job);
        }
        
        setCurrentJobs(jobMap);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start translation');
      setIsProcessing(false);
    }
  };

  // Retry failed job
  const handleRetryJob = async (regionId: string) => {
    const job = currentJobs.get(regionId);
    if (!job) return;

    try {
      const retriedJob = await translationAPI.retryTranslationJob(job.id);
      setCurrentJobs(prev => new Map(prev.set(regionId, retriedJob)));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to retry translation');
    }
  };

  // Select different translation alternative
  const handleSelectAlternative = (regionId: string, alternativeId: string) => {
    const completedJob = completedJobs.get(regionId);
    if (!completedJob) return;

    const selectedAlternative = completedJob.alternatives.find(alt => alt.id === alternativeId);
    if (selectedAlternative) {
      onTranslationComplete(regionId, selectedAlternative.translated_text);
      
      // Update the alternatives to mark the new selection
      const updatedJob = {
        ...completedJob,
        alternatives: completedJob.alternatives.map(alt => ({
          ...alt,
          is_selected: alt.id === alternativeId
        }))
      };
      setCompletedJobs(prev => new Map(prev.set(regionId, updatedJob)));
    }
  };

  const getProviderIcon = (provider: LLMProvider) => {
    switch (provider) {
      case LLMProvider.CLAUDE:
        return '🤖';
      case LLMProvider.OPENAI:
        return '🧠';
      case LLMProvider.GEMINI:
        return '💎';
      case LLMProvider.DEEPSEEK:
        return '🔍';
      default:
        return '🤖';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600';
      case 'in_progress':
        return 'text-blue-600';
      case 'failed':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">Translation Processing</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      <div className="p-4 space-y-4">
        {/* Translation Info */}
        <div className="bg-blue-50 rounded-lg p-3">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-blue-900">Translation Task</h4>
              <p className="text-sm text-blue-700">
                {SUPPORTED_LANGUAGES[sourceLanguage as keyof typeof SUPPORTED_LANGUAGES]} → {SUPPORTED_LANGUAGES[targetLanguage as keyof typeof SUPPORTED_LANGUAGES]}
              </p>
            </div>
            <div className="text-right">
              <div className="text-lg font-bold text-blue-900">{regionsToTranslate.length}</div>
              <div className="text-sm text-blue-700">regions</div>
            </div>
          </div>
        </div>

        {/* Configuration */}
        {!isProcessing && currentJobs.size === 0 && (
          <>
            {/* Translation Mode */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Translation Mode
              </label>
              <div className="grid grid-cols-2 gap-2">
                <label className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
                  translationMode === 'single'
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}>
                  <input
                    type="radio"
                    name="mode"
                    value="single"
                    checked={translationMode === 'single'}
                    onChange={(e) => setTranslationMode(e.target.value as 'single' | 'batch')}
                    className="sr-only"
                  />
                  <div>
                    <div className="font-medium text-gray-900">Single</div>
                    <div className="text-sm text-gray-600">Process one by one</div>
                  </div>
                </label>
                <label className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
                  translationMode === 'batch'
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                } ${regionsToTranslate.length <= 1 ? 'opacity-50 cursor-not-allowed' : ''}`}>
                  <input
                    type="radio"
                    name="mode"
                    value="batch"
                    checked={translationMode === 'batch'}
                    onChange={(e) => setTranslationMode(e.target.value as 'single' | 'batch')}
                    disabled={regionsToTranslate.length <= 1}
                    className="sr-only"
                  />
                  <div>
                    <div className="font-medium text-gray-900">Batch</div>
                    <div className="text-sm text-gray-600">Process all together</div>
                  </div>
                </label>
              </div>
            </div>

            {/* Provider Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Translation Provider
              </label>
              <div className="grid grid-cols-1 gap-2">
                {providers?.providers.map((provider) => (
                  <label
                    key={provider.provider}
                    className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedProvider === provider.provider
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    } ${!provider.is_available ? 'opacity-50 cursor-not-allowed' : ''}`}
                  >
                    <input
                      type="radio"
                      name="provider"
                      value={provider.provider}
                      checked={selectedProvider === provider.provider}
                      onChange={(e) => setSelectedProvider(e.target.value as LLMProvider)}
                      disabled={!provider.is_available}
                      className="sr-only"
                    />
                    <div className="flex items-center space-x-3 flex-1">
                      <span className="text-2xl">{getProviderIcon(provider.provider)}</span>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium text-gray-900 capitalize">
                            {provider.provider}
                          </span>
                          {!provider.is_available && (
                            <span className="px-2 py-0.5 bg-red-100 text-red-800 text-xs rounded-full">
                              Unavailable
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-600">
                          {provider.available_models.length} models available
                        </p>
                      </div>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {/* Custom Prompt */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Custom Prompt (Optional)
              </label>
              <textarea
                value={customPrompt}
                onChange={(e) => setCustomPrompt(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter custom instructions for translation..."
              />
              <p className="text-xs text-gray-500 mt-1">
                Leave empty to use default translation prompt
              </p>
            </div>

            {/* Start Button */}
            <button
              onClick={handleStartTranslation}
              disabled={regionsToTranslate.length === 0 || !providers?.providers.some(p => p.is_available)}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Start Translation ({regionsToTranslate.length} regions)
            </button>
          </>
        )}

        {/* Processing Status */}
        {(isProcessing || currentJobs.size > 0) && (
          <div className="space-y-4">
            {/* Progress */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">Progress</span>
                <span className="text-sm text-gray-600">{Math.round(progress)}%</span>
              </div>
              <div className="bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
            </div>

            {/* Job Status */}
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {Array.from(currentJobs.entries()).map(([regionId, job]) => {
                const region = textRegions.find(r => r.id === regionId);
                const completedJob = completedJobs.get(regionId);
                
                return (
                  <div key={regionId} className="bg-gray-50 rounded-lg p-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <span className={`font-medium capitalize ${getStatusColor(job.status)}`}>
                            {job.status.replace('_', ' ')}
                          </span>
                          {job.processing_time && (
                            <span className="text-xs text-gray-500">
                              {job.processing_time}s
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 mt-1">
                          {region?.original_text || 'Unknown text'}
                        </p>
                        
                        {/* Translation Results */}
                        {completedJob && completedJob.alternatives.length > 0 && (
                          <div className="mt-2 space-y-1">
                            {completedJob.alternatives.map((alt, index) => (
                              <div
                                key={alt.id}
                                className={`p-2 rounded border cursor-pointer transition-colors ${
                                  alt.is_selected
                                    ? 'border-blue-500 bg-blue-50'
                                    : 'border-gray-200 hover:border-gray-300'
                                }`}
                                onClick={() => handleSelectAlternative(regionId, alt.id)}
                              >
                                <div className="flex items-start justify-between">
                                  <p className="text-sm text-gray-900 flex-1">
                                    {alt.translated_text}
                                  </p>
                                  <div className="ml-2 text-xs text-gray-500">
                                    {alt.confidence_score && `${Math.round(alt.confidence_score * 100)}%`}
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                        
                        {job.error_message && (
                          <p className="text-sm text-red-600 mt-1">{job.error_message}</p>
                        )}
                      </div>
                      
                      {job.status === 'failed' && (
                        <button
                          onClick={() => handleRetryJob(regionId)}
                          className="ml-2 p-1 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded"
                          title="Retry translation"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                          </svg>
                        </button>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h4 className="font-medium text-red-800">Error</h4>
            </div>
            <p className="text-sm text-red-700 mt-1">{error}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default TranslationProcessor;
