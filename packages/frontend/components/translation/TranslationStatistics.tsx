'use client';

import React, { useState, useEffect } from 'react';
import { TranslationStatistics as TranslationStatsType } from '@/types/api';
import { translationAPI } from '@/lib/api-client';

interface TranslationStatisticsProps {
  className?: string;
}

export const TranslationStatistics: React.FC<TranslationStatisticsProps> = ({
  className = ''
}) => {
  const [stats, setStats] = useState<TranslationStatsType | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load translation statistics
  useEffect(() => {
    const loadStats = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const statistics = await translationAPI.getTranslationStatistics();
        setStats(statistics);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load translation statistics');
      } finally {
        setIsLoading(false);
      }
    };

    loadStats();
  }, []);

  const getProviderIcon = (provider: string) => {
    switch (provider.toLowerCase()) {
      case 'claude':
        return '🤖';
      case 'openai':
        return '🧠';
      case 'gemini':
        return '💎';
      case 'deepseek':
        return '🔍';
      default:
        return '🤖';
    }
  };

  const formatPercentage = (value: number) => {
    return `${Math.round(value * 100)}%`;
  };

  const formatTime = (seconds: number) => {
    if (seconds < 60) {
      return `${seconds.toFixed(1)}s`;
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds.toFixed(0)}s`;
  };

  const getLanguagePairDisplay = (pair: string) => {
    const [source, target] = pair.split('-');
    return `${source} → ${target}`;
  };

  if (isLoading) {
    return (
      <div className={`bg-white border border-gray-200 rounded-lg shadow-sm p-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-2 gap-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white border border-gray-200 rounded-lg shadow-sm p-4 ${className}`}>
        <div className="text-center text-red-600">
          <svg className="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <p className="text-sm">{error}</p>
        </div>
      </div>
    );
  }

  if (!stats) {
    return null;
  }

  const successRate = stats.total_jobs > 0 ? stats.completed_jobs / stats.total_jobs : 0;

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900">Translation Statistics</h3>
      </div>

      <div className="p-4 space-y-6">
        {/* Overview Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-blue-50 rounded-lg p-3">
            <div className="text-2xl font-bold text-blue-600">{stats.total_jobs}</div>
            <div className="text-sm text-blue-800">Total Jobs</div>
          </div>
          
          <div className="bg-green-50 rounded-lg p-3">
            <div className="text-2xl font-bold text-green-600">{stats.completed_jobs}</div>
            <div className="text-sm text-green-800">Completed</div>
          </div>
          
          <div className="bg-yellow-50 rounded-lg p-3">
            <div className="text-2xl font-bold text-yellow-600">{stats.pending_jobs + stats.processing_jobs}</div>
            <div className="text-sm text-yellow-800">In Progress</div>
          </div>
          
          <div className="bg-red-50 rounded-lg p-3">
            <div className="text-2xl font-bold text-red-600">{stats.failed_jobs}</div>
            <div className="text-sm text-red-800">Failed</div>
          </div>
        </div>

        {/* Success Rate */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">Success Rate</span>
            <span className="text-sm text-gray-600">{formatPercentage(successRate)}</span>
          </div>
          <div className="bg-gray-200 rounded-full h-2">
            <div
              className="bg-green-600 h-2 rounded-full"
              style={{ width: `${successRate * 100}%` }}
            ></div>
          </div>
        </div>

        {/* Performance Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {stats.average_processing_time && (
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="text-lg font-semibold text-gray-900">
                {formatTime(stats.average_processing_time)}
              </div>
              <div className="text-sm text-gray-600">Avg Processing Time</div>
            </div>
          )}
          
          {stats.average_confidence_score && (
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="text-lg font-semibold text-gray-900">
                {formatPercentage(stats.average_confidence_score)}
              </div>
              <div className="text-sm text-gray-600">Avg Confidence</div>
            </div>
          )}

          {stats.average_quality_score && (
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="text-lg font-semibold text-gray-900">
                {formatPercentage(stats.average_quality_score)}
              </div>
              <div className="text-sm text-gray-600">Avg Quality</div>
            </div>
          )}
        </div>

        {/* Language Pairs */}
        {Object.keys(stats.language_pairs).length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-3">Language Pairs</h4>
            <div className="space-y-2">
              {Object.entries(stats.language_pairs)
                .sort(([, a], [, b]) => b - a)
                .map(([pair, count]) => {
                  const percentage = stats.total_jobs > 0 ? (count / stats.total_jobs) * 100 : 0;
                  return (
                    <div key={pair} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">🌐</span>
                        <span className="text-sm font-medium text-gray-900">
                          {getLanguagePairDisplay(pair)}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-24 bg-gray-200 rounded-full h-1.5">
                          <div
                            className="bg-blue-600 h-1.5 rounded-full"
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                        <span className="text-sm text-gray-600 w-12 text-right">
                          {count} ({Math.round(percentage)}%)
                        </span>
                      </div>
                    </div>
                  );
                })}
            </div>
          </div>
        )}

        {/* Status Breakdown */}
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-3">Job Status Breakdown</h4>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Pending:</span>
              <span className="font-medium">{stats.pending_jobs}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Processing:</span>
              <span className="font-medium">{stats.processing_jobs}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Completed:</span>
              <span className="font-medium text-green-600">{stats.completed_jobs}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Failed:</span>
              <span className="font-medium text-red-600">{stats.failed_jobs}</span>
            </div>
          </div>
        </div>

        {/* Quality Indicators */}
        <div className="border-t border-gray-200 pt-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
            <div className={`p-3 rounded-lg ${successRate >= 0.9 ? 'bg-green-50' : successRate >= 0.7 ? 'bg-yellow-50' : 'bg-red-50'}`}>
              <div className={`text-sm font-medium ${successRate >= 0.9 ? 'text-green-800' : successRate >= 0.7 ? 'text-yellow-800' : 'text-red-800'}`}>
                {successRate >= 0.9 ? 'Excellent' : successRate >= 0.7 ? 'Good' : 'Needs Attention'}
              </div>
              <div className="text-xs text-gray-600">Success Rate</div>
            </div>
            
            <div className={`p-3 rounded-lg ${stats.average_processing_time && stats.average_processing_time < 30 ? 'bg-green-50' : stats.average_processing_time && stats.average_processing_time < 60 ? 'bg-yellow-50' : 'bg-red-50'}`}>
              <div className={`text-sm font-medium ${stats.average_processing_time && stats.average_processing_time < 30 ? 'text-green-800' : stats.average_processing_time && stats.average_processing_time < 60 ? 'text-yellow-800' : 'text-red-800'}`}>
                {stats.average_processing_time && stats.average_processing_time < 30 ? 'Fast' : stats.average_processing_time && stats.average_processing_time < 60 ? 'Moderate' : 'Slow'}
              </div>
              <div className="text-xs text-gray-600">Processing Speed</div>
            </div>
            
            <div className={`p-3 rounded-lg ${stats.average_quality_score && stats.average_quality_score >= 0.8 ? 'bg-green-50' : stats.average_quality_score && stats.average_quality_score >= 0.6 ? 'bg-yellow-50' : 'bg-red-50'}`}>
              <div className={`text-sm font-medium ${stats.average_quality_score && stats.average_quality_score >= 0.8 ? 'text-green-800' : stats.average_quality_score && stats.average_quality_score >= 0.6 ? 'text-yellow-800' : 'text-red-800'}`}>
                {stats.average_quality_score && stats.average_quality_score >= 0.8 ? 'High' : stats.average_quality_score && stats.average_quality_score >= 0.6 ? 'Medium' : 'Low'}
              </div>
              <div className="text-xs text-gray-600">Quality Score</div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="border-t border-gray-200 pt-4">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Activity Summary</h4>
          <div className="text-sm text-gray-600">
            <p>
              Total of <span className="font-medium text-gray-900">{stats.total_jobs}</span> translation jobs processed
              {stats.total_jobs > 0 && (
                <>
                  {' '}with a <span className="font-medium text-gray-900">{formatPercentage(successRate)}</span> success rate
                  {stats.average_processing_time && (
                    <> and an average processing time of <span className="font-medium text-gray-900">{formatTime(stats.average_processing_time)}</span></>
                  )}
                </>
              )}
              .
            </p>
            {Object.keys(stats.language_pairs).length > 0 && (
              <p className="mt-1">
                Most popular language pair: <span className="font-medium text-gray-900">
                  {getLanguagePairDisplay(
                    Object.entries(stats.language_pairs)
                      .sort(([, a], [, b]) => b - a)[0][0]
                  )}
                </span>
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TranslationStatistics;
