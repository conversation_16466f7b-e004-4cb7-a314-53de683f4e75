'use client';

import React, { useState, useEffect } from 'react';
import { TranslationJobResponse, TranslationJobDetailResponse } from '@/types/api';
import { translationAPI } from '@/lib/api-client';

interface TranslationJobHistoryProps {
  regionId: string;
  onJobSelect?: (job: TranslationJobDetailResponse) => void;
  className?: string;
}

export const TranslationJobHistory: React.FC<TranslationJobHistoryProps> = ({
  regionId,
  onJobSelect,
  className = ''
}) => {
  const [jobs, setJobs] = useState<TranslationJobResponse[]>([]);
  const [selectedJob, setSelectedJob] = useState<TranslationJobDetailResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load translation jobs for the region
  useEffect(() => {
    const loadJobs = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const regionJobs = await translationAPI.getRegionTranslationJobs(regionId);
        setJobs(regionJobs.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()));
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load translation jobs');
      } finally {
        setIsLoading(false);
      }
    };

    if (regionId) {
      loadJobs();
    }
  }, [regionId]);

  // Load job details
  const handleJobClick = async (job: TranslationJobResponse) => {
    try {
      const jobDetail = await translationAPI.getTranslationJobDetail(job.id);
      setSelectedJob(jobDetail);
      onJobSelect?.(jobDetail);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load job details');
    }
  };

  // Retry failed job
  const handleRetryJob = async (jobId: string) => {
    try {
      await translationAPI.retryTranslationJob(jobId);
      // Reload jobs to get updated status
      const regionJobs = await translationAPI.getRegionTranslationJobs(regionId);
      setJobs(regionJobs.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to retry job');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getProviderIcon = (provider: string) => {
    switch (provider.toLowerCase()) {
      case 'claude':
        return '🤖';
      case 'openai':
        return '🧠';
      case 'gemini':
        return '💎';
      case 'deepseek':
        return '🔍';
      default:
        return '🤖';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return (
      <div className={`bg-white border border-gray-200 rounded-lg shadow-sm p-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900">
          Translation History ({jobs.length})
        </h3>
        {error && (
          <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-600">
            {error}
          </div>
        )}
      </div>

      {/* Job List */}
      <div className="divide-y divide-gray-200">
        {jobs.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            <svg className="w-12 h-12 mx-auto mb-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
            </svg>
            <p className="text-sm">No translation jobs found</p>
            <p className="text-xs text-gray-400 mt-1">Start translation to see job history</p>
          </div>
        ) : (
          jobs.map((job) => (
            <div
              key={job.id}
              onClick={() => handleJobClick(job)}
              className="p-4 cursor-pointer hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3 flex-1">
                  {/* Provider Icon */}
                  <div className="flex-shrink-0 mt-1">
                    <span className="text-lg">{getProviderIcon(job.provider)}</span>
                  </div>

                  {/* Job Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <h4 className="text-sm font-medium text-gray-900 capitalize">
                        {job.provider} Translation
                      </h4>
                      <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${getStatusColor(job.status)}`}>
                        {job.status.replace('_', ' ')}
                      </span>
                    </div>
                    
                    <div className="mt-1 text-sm text-gray-600">
                      <div className="flex items-center space-x-4">
                        <span>{formatDate(job.created_at)}</span>
                        <span>{job.source_language} → {job.target_language}</span>
                        {job.alternative_count > 0 && (
                          <span>{job.alternative_count} alternatives</span>
                        )}
                        {job.processing_time && (
                          <span>{job.processing_time}s processing time</span>
                        )}
                      </div>
                    </div>

                    {/* Original Text */}
                    <div className="mt-2 text-sm">
                      <div className="text-xs text-gray-500 mb-1">Original:</div>
                      <div className="text-gray-900 bg-gray-50 rounded p-2">
                        {job.original_text}
                      </div>
                    </div>

                    {job.custom_prompt && (
                      <div className="mt-2 text-xs text-gray-500 bg-gray-50 rounded p-2">
                        <span className="font-medium">Custom prompt:</span> {job.custom_prompt}
                      </div>
                    )}

                    {job.error_message && (
                      <div className="mt-2 text-xs text-red-600 bg-red-50 rounded p-2">
                        <span className="font-medium">Error:</span> {job.error_message}
                      </div>
                    )}
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center space-x-2 ml-4">
                  {job.status === 'failed' && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRetryJob(job.id);
                      }}
                      className="p-1 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded"
                      title="Retry job"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                    </button>
                  )}
                  
                  {job.status === 'in_progress' && (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  )}
                  
                  {job.status === 'completed' && (
                    <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Selected Job Details */}
      {selectedJob && (
        <div className="border-t border-gray-200 bg-gray-50">
          <div className="p-4">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium text-gray-900">Translation Alternatives</h4>
              <button
                onClick={() => setSelectedJob(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {selectedJob.alternatives.length > 0 ? (
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {selectedJob.alternatives
                  .sort((a, b) => b.rank - a.rank)
                  .map((alternative, index) => (
                    <div 
                      key={alternative.id} 
                      className={`bg-white rounded p-3 text-sm border ${
                        alternative.is_selected ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                      }`}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <span className="font-medium text-gray-900">
                              Alternative {alternative.rank}
                            </span>
                            {alternative.is_selected && (
                              <span className="px-2 py-0.5 bg-blue-100 text-blue-800 text-xs rounded-full">
                                Selected
                              </span>
                            )}
                          </div>
                          <div className="text-gray-700">
                            {alternative.translated_text}
                          </div>
                          <div className="flex items-center space-x-4 text-xs text-gray-500 mt-1">
                            {alternative.confidence_score && (
                              <span>Confidence: {Math.round(alternative.confidence_score * 100)}%</span>
                            )}
                            {alternative.quality_score && (
                              <span>Quality: {Math.round(alternative.quality_score * 100)}%</span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500">No alternatives found for this job.</p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default TranslationJobHistory;
