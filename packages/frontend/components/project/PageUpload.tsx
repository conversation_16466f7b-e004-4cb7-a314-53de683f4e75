'use client';

import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { ProjectPageResponse } from '@/types/api';
import { projectsAPI } from '@/lib/api-client';
import { validateImageFile, createImagePreview, ProgressTracker } from '@/lib/api-utils';

interface PageUploadProps {
  projectId: string;
  onPageUploaded: (page: ProjectPageResponse) => void;
  onClose: () => void;
  className?: string;
}

interface UploadFile {
  file: File;
  preview: string;
  pageNumber: number;
  status: 'pending' | 'uploading' | 'success' | 'error';
  progress: number;
  error?: string;
  result?: ProjectPageResponse;
}

export const PageUpload: React.FC<PageUploadProps> = ({
  projectId,
  onPageUploaded,
  onClose,
  className = ''
}) => {
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [nextPageNumber, setNextPageNumber] = useState(1);

  // Handle file drop
  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const newFiles: UploadFile[] = [];
    
    for (let i = 0; i < acceptedFiles.length; i++) {
      const file = acceptedFiles[i];
      const validation = validateImageFile(file);
      
      if (validation.isValid) {
        try {
          const preview = await createImagePreview(file);
          newFiles.push({
            file,
            preview,
            pageNumber: nextPageNumber + i,
            status: 'pending',
            progress: 0
          });
        } catch (error) {
          newFiles.push({
            file,
            preview: '',
            pageNumber: nextPageNumber + i,
            status: 'error',
            progress: 0,
            error: 'Failed to create preview'
          });
        }
      } else {
        newFiles.push({
          file,
          preview: '',
          pageNumber: nextPageNumber + i,
          status: 'error',
          progress: 0,
          error: validation.error
        });
      }
    }
    
    setUploadFiles(prev => [...prev, ...newFiles]);
    setNextPageNumber(prev => prev + acceptedFiles.length);
  }, [nextPageNumber]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.webp']
    },
    multiple: true,
    disabled: isUploading
  });

  // Remove file from upload list
  const removeFile = (index: number) => {
    setUploadFiles(prev => prev.filter((_, i) => i !== index));
  };

  // Update page number for a file
  const updatePageNumber = (index: number, pageNumber: number) => {
    setUploadFiles(prev => prev.map((file, i) => 
      i === index ? { ...file, pageNumber } : file
    ));
  };

  // Upload single file
  const uploadFile = async (file: UploadFile, index: number): Promise<void> => {
    try {
      // Update status to uploading
      setUploadFiles(prev => prev.map((f, i) => 
        i === index ? { ...f, status: 'uploading', progress: 0 } : f
      ));

      // Create progress tracker
      const progressTracker = new ProgressTracker();
      progressTracker.addCallback((progress) => {
        setUploadFiles(prev => prev.map((f, i) => 
          i === index ? { ...f, progress } : f
        ));
      });

      // Simulate progress for demo (in real implementation, this would come from upload progress)
      const progressInterval = setInterval(() => {
        progressTracker.updateProgress(progressTracker.progress + 10);
        if (progressTracker.progress >= 90) {
          clearInterval(progressInterval);
        }
      }, 200);

      // Upload the file
      const result = await projectsAPI.uploadPage(projectId, file.file, file.pageNumber);
      
      clearInterval(progressInterval);
      progressTracker.complete();

      // Update status to success
      setUploadFiles(prev => prev.map((f, i) => 
        i === index ? { ...f, status: 'success', progress: 100, result } : f
      ));

      onPageUploaded(result);
    } catch (error) {
      // Update status to error
      setUploadFiles(prev => prev.map((f, i) => 
        i === index ? { 
          ...f, 
          status: 'error', 
          progress: 0, 
          error: error instanceof Error ? error.message : 'Upload failed' 
        } : f
      ));
    }
  };

  // Upload all files
  const uploadAllFiles = async () => {
    setIsUploading(true);
    
    const pendingFiles = uploadFiles.filter(f => f.status === 'pending');
    
    // Upload files sequentially to avoid overwhelming the server
    for (let i = 0; i < pendingFiles.length; i++) {
      const fileIndex = uploadFiles.findIndex(f => f === pendingFiles[i]);
      if (fileIndex !== -1) {
        await uploadFile(pendingFiles[i], fileIndex);
      }
    }
    
    setIsUploading(false);
  };

  // Check if all uploads are complete
  const allUploadsComplete = uploadFiles.length > 0 && uploadFiles.every(f => 
    f.status === 'success' || f.status === 'error'
  );

  const getStatusIcon = (status: UploadFile['status']) => {
    switch (status) {
      case 'pending':
        return (
          <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'uploading':
        return (
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
        );
      case 'success':
        return (
          <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        );
      case 'error':
        return (
          <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        );
    }
  };

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">Upload Pages</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      {/* Upload Area */}
      <div className="p-4">
        <div
          {...getRootProps()}
          className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
            isDragActive
              ? 'border-blue-400 bg-blue-50'
              : 'border-gray-300 hover:border-gray-400'
          } ${isUploading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
        >
          <input {...getInputProps()} />
          <svg className="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
          </svg>
          <p className="text-lg font-medium text-gray-900 mb-2">
            {isDragActive ? 'Drop images here' : 'Upload manga pages'}
          </p>
          <p className="text-sm text-gray-600">
            Drag and drop images, or click to select files
          </p>
          <p className="text-xs text-gray-500 mt-2">
            Supports JPEG, PNG, WebP (max 50MB each)
          </p>
        </div>
      </div>

      {/* File List */}
      {uploadFiles.length > 0 && (
        <div className="border-t border-gray-200">
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-sm font-medium text-gray-900">
                Files to Upload ({uploadFiles.length})
              </h4>
              {uploadFiles.some(f => f.status === 'pending') && (
                <button
                  onClick={uploadAllFiles}
                  disabled={isUploading}
                  className="px-3 py-1.5 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 disabled:opacity-50"
                >
                  {isUploading ? 'Uploading...' : 'Upload All'}
                </button>
              )}
            </div>

            <div className="space-y-3 max-h-64 overflow-y-auto">
              {uploadFiles.map((file, index) => (
                <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                  {/* Preview */}
                  <div className="flex-shrink-0">
                    {file.preview ? (
                      <img
                        src={file.preview}
                        alt={file.file.name}
                        className="w-12 h-12 object-cover rounded"
                      />
                    ) : (
                      <div className="w-12 h-12 bg-gray-200 rounded flex items-center justify-center">
                        <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                      </div>
                    )}
                  </div>

                  {/* File Info */}
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {file.file.name}
                    </p>
                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span>{(file.file.size / 1024 / 1024).toFixed(1)} MB</span>
                      <span>Page</span>
                      <input
                        type="number"
                        min="1"
                        value={file.pageNumber}
                        onChange={(e) => updatePageNumber(index, parseInt(e.target.value) || 1)}
                        disabled={file.status === 'uploading' || file.status === 'success'}
                        className="w-16 px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 disabled:bg-gray-100"
                      />
                    </div>
                    
                    {/* Progress Bar */}
                    {file.status === 'uploading' && (
                      <div className="mt-2">
                        <div className="bg-gray-200 rounded-full h-1.5">
                          <div
                            className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
                            style={{ width: `${file.progress}%` }}
                          ></div>
                        </div>
                      </div>
                    )}
                    
                    {/* Error Message */}
                    {file.error && (
                      <p className="mt-1 text-xs text-red-600">{file.error}</p>
                    )}
                  </div>

                  {/* Status */}
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(file.status)}
                    {file.status !== 'uploading' && file.status !== 'success' && (
                      <button
                        onClick={() => removeFile(index)}
                        className="text-gray-400 hover:text-red-600"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* Summary */}
            {allUploadsComplete && (
              <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <p className="text-sm text-green-800">
                    Upload complete! {uploadFiles.filter(f => f.status === 'success').length} of {uploadFiles.length} files uploaded successfully.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default PageUpload;
