'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import { useToastHelpers } from '@/components/feedback/ToastNotification';

// Async operation state
interface AsyncOperationState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

// Async operation options
interface AsyncOperationOptions {
  showSuccessToast?: boolean;
  showErrorToast?: boolean;
  successMessage?: string;
  errorMessage?: string;
  retryCount?: number;
  retryDelay?: number;
  timeout?: number;
}

// Async operation hook
export const useAsyncOperation = <T = any>(
  operation: () => Promise<T>,
  options: AsyncOperationOptions = {}
) => {
  const {
    showSuccessToast = false,
    showErrorToast = true,
    successMessage = 'Operation completed successfully',
    errorMessage = 'Operation failed',
    retryCount = 0,
    retryDelay = 1000,
    timeout = 30000
  } = options;

  const [state, setState] = useState<AsyncOperationState<T>>({
    data: null,
    loading: false,
    error: null,
    lastUpdated: null
  });

  const toast = useToastHelpers();
  const abortControllerRef = useRef<AbortController | null>(null);
  const retryCountRef = useRef(0);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  const execute = useCallback(async (): Promise<T | null> => {
    // Cancel previous operation
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();
    const { signal } = abortControllerRef.current;

    setState(prev => ({
      ...prev,
      loading: true,
      error: null
    }));

    try {
      // Set timeout
      const timeoutId = setTimeout(() => {
        abortControllerRef.current?.abort();
      }, timeout);

      // Execute operation
      const result = await operation();

      // Clear timeout
      clearTimeout(timeoutId);

      // Check if operation was aborted
      if (signal.aborted) {
        throw new Error('Operation was cancelled');
      }

      // Update state with success
      setState({
        data: result,
        loading: false,
        error: null,
        lastUpdated: new Date()
      });

      // Show success toast
      if (showSuccessToast) {
        toast.success(successMessage);
      }

      // Reset retry count
      retryCountRef.current = 0;

      return result;
    } catch (error) {
      // Check if operation was aborted
      if (signal.aborted) {
        setState(prev => ({
          ...prev,
          loading: false
        }));
        return null;
      }

      const errorMsg = error instanceof Error ? error.message : String(error);

      // Handle retries
      if (retryCountRef.current < retryCount) {
        retryCountRef.current++;
        
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        
        // Retry operation
        return execute();
      }

      // Update state with error
      setState({
        data: null,
        loading: false,
        error: errorMsg,
        lastUpdated: new Date()
      });

      // Show error toast
      if (showErrorToast) {
        toast.error(`${errorMessage}: ${errorMsg}`);
      }

      // Reset retry count
      retryCountRef.current = 0;

      throw error;
    }
  }, [operation, showSuccessToast, showErrorToast, successMessage, errorMessage, retryCount, retryDelay, timeout, toast]);

  const cancel = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, []);

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      lastUpdated: null
    });
    retryCountRef.current = 0;
  }, []);

  return {
    ...state,
    execute,
    cancel,
    reset,
    isIdle: !state.loading && !state.error && !state.data,
    isSuccess: !state.loading && !state.error && state.data !== null,
    isError: !state.loading && state.error !== null
  };
};

// Hook for handling multiple async operations
export const useAsyncOperations = () => {
  const [operations, setOperations] = useState<Map<string, AsyncOperationState<any>>>(new Map());

  const addOperation = useCallback(<T>(
    key: string,
    operation: () => Promise<T>,
    options?: AsyncOperationOptions
  ) => {
    const asyncOp = useAsyncOperation(operation, options);
    
    setOperations(prev => new Map(prev.set(key, {
      data: asyncOp.data,
      loading: asyncOp.loading,
      error: asyncOp.error,
      lastUpdated: asyncOp.lastUpdated
    })));

    return asyncOp;
  }, []);

  const getOperation = useCallback((key: string) => {
    return operations.get(key);
  }, [operations]);

  const removeOperation = useCallback((key: string) => {
    setOperations(prev => {
      const newMap = new Map(prev);
      newMap.delete(key);
      return newMap;
    });
  }, []);

  const clearOperations = useCallback(() => {
    setOperations(new Map());
  }, []);

  const hasLoadingOperations = Array.from(operations.values()).some(op => op.loading);
  const hasErrorOperations = Array.from(operations.values()).some(op => op.error);

  return {
    operations,
    addOperation,
    getOperation,
    removeOperation,
    clearOperations,
    hasLoadingOperations,
    hasErrorOperations
  };
};

// Hook for API calls with automatic error handling
export const useApiCall = <T = any>(
  apiFunction: (...args: any[]) => Promise<T>,
  options: AsyncOperationOptions = {}
) => {
  const operation = useCallback(() => apiFunction(), [apiFunction]);
  
  return useAsyncOperation(operation, {
    showErrorToast: true,
    errorMessage: 'API request failed',
    retryCount: 1,
    retryDelay: 1000,
    timeout: 30000,
    ...options
  });
};

// Hook for file operations
export const useFileOperation = <T = any>(
  fileOperation: () => Promise<T>,
  options: AsyncOperationOptions = {}
) => {
  return useAsyncOperation(fileOperation, {
    showSuccessToast: true,
    showErrorToast: true,
    successMessage: 'File operation completed',
    errorMessage: 'File operation failed',
    timeout: 60000, // Longer timeout for file operations
    ...options
  });
};

// Hook for form submissions
export const useFormSubmission = <T = any>(
  submitFunction: (data: any) => Promise<T>,
  options: AsyncOperationOptions = {}
) => {
  const [formData, setFormData] = useState<any>(null);
  
  const operation = useCallback(() => {
    if (!formData) {
      throw new Error('No form data provided');
    }
    return submitFunction(formData);
  }, [submitFunction, formData]);

  const asyncOp = useAsyncOperation(operation, {
    showSuccessToast: true,
    showErrorToast: true,
    successMessage: 'Form submitted successfully',
    errorMessage: 'Form submission failed',
    ...options
  });

  const submit = useCallback(async (data: any) => {
    setFormData(data);
    return asyncOp.execute();
  }, [asyncOp.execute]);

  return {
    ...asyncOp,
    submit
  };
};

export default useAsyncOperation;
