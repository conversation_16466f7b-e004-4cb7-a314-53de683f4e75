'use client';

import { useEffect, useCallback } from 'react';
import { CanvasTool } from '@/types/canvas';

interface KeyboardShortcutsConfig {
  onZoomIn?: () => void;
  onZoomOut?: () => void;
  onResetZoom?: () => void;
  onFitToScreen?: () => void;
  onToolChange?: (tool: CanvasTool) => void;
  onUndo?: () => void;
  onRedo?: () => void;
  onSave?: () => void;
  onDelete?: () => void;
  onSelectAll?: () => void;
  onDeselect?: () => void;
  onCopy?: () => void;
  onPaste?: () => void;
  onDuplicate?: () => void;
  onPan?: (deltaX: number, deltaY: number) => void;
  enabled?: boolean;
}

export const useKeyboardShortcuts = (config: KeyboardShortcutsConfig) => {
  const {
    onZoomIn,
    onZoomOut,
    onResetZoom,
    onFitToScreen,
    onToolChange,
    onUndo,
    onRedo,
    onSave,
    onDelete,
    onSelectAll,
    onDeselect,
    onCopy,
    onPaste,
    onDuplicate,
    onPan,
    enabled = true
  } = config;

  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (!enabled) return;

    // Don't trigger shortcuts when typing in inputs
    const target = e.target as HTMLElement;
    if (
      target.tagName === 'INPUT' ||
      target.tagName === 'TEXTAREA' ||
      target.contentEditable === 'true'
    ) {
      return;
    }

    const isCtrl = e.ctrlKey || e.metaKey;
    const isShift = e.shiftKey;
    const isAlt = e.altKey;

    // Prevent default for our shortcuts
    const shouldPreventDefault = () => {
      // Zoom shortcuts
      if (isCtrl && (e.code === 'Equal' || e.code === 'Minus' || e.code === 'Digit0')) {
        return true;
      }
      
      // Tool shortcuts
      if (!isCtrl && !isAlt && ['KeyV', 'KeyT', 'KeyH', 'Space'].includes(e.code)) {
        return true;
      }
      
      // Action shortcuts
      if (isCtrl && ['KeyZ', 'KeyY', 'KeyS', 'KeyA', 'KeyC', 'KeyV', 'KeyD'].includes(e.code)) {
        return true;
      }
      
      // Navigation shortcuts
      if (isShift && ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(e.code)) {
        return true;
      }
      
      // Delete key
      if (e.code === 'Delete' || e.code === 'Backspace') {
        return true;
      }
      
      // Escape key
      if (e.code === 'Escape') {
        return true;
      }

      return false;
    };

    if (shouldPreventDefault()) {
      e.preventDefault();
    }

    // Handle shortcuts
    switch (e.code) {
      // Zoom controls
      case 'Equal':
        if (isCtrl && onZoomIn) {
          onZoomIn();
        }
        break;
      
      case 'Minus':
        if (isCtrl && onZoomOut) {
          onZoomOut();
        }
        break;
      
      case 'Digit0':
        if (isCtrl) {
          if (isShift && onFitToScreen) {
            onFitToScreen();
          } else if (onResetZoom) {
            onResetZoom();
          }
        }
        break;

      // Tool selection
      case 'KeyV':
        if (!isCtrl && !isAlt && onToolChange) {
          onToolChange(CanvasTool.SELECT);
        }
        break;
      
      case 'KeyT':
        if (!isCtrl && !isAlt && onToolChange) {
          onToolChange(CanvasTool.TEXT_REGION);
        }
        break;
      
      case 'KeyH':
      case 'Space':
        if (!isCtrl && !isAlt && onToolChange) {
          onToolChange(CanvasTool.PAN);
        }
        break;

      // History actions
      case 'KeyZ':
        if (isCtrl) {
          if (isShift && onRedo) {
            onRedo();
          } else if (onUndo) {
            onUndo();
          }
        }
        break;
      
      case 'KeyY':
        if (isCtrl && onRedo) {
          onRedo();
        }
        break;

      // File actions
      case 'KeyS':
        if (isCtrl && onSave) {
          onSave();
        }
        break;

      // Selection actions
      case 'KeyA':
        if (isCtrl && onSelectAll) {
          onSelectAll();
        }
        break;
      
      case 'Escape':
        if (onDeselect) {
          onDeselect();
        }
        break;

      // Clipboard actions
      case 'KeyC':
        if (isCtrl && onCopy) {
          onCopy();
        }
        break;
      
      case 'KeyV':
        if (isCtrl && onPaste) {
          onPaste();
        }
        break;
      
      case 'KeyD':
        if (isCtrl && onDuplicate) {
          onDuplicate();
        }
        break;

      // Delete actions
      case 'Delete':
      case 'Backspace':
        if (onDelete) {
          onDelete();
        }
        break;

      // Pan navigation
      case 'ArrowUp':
        if (isShift && onPan) {
          onPan(0, 20);
        }
        break;
      
      case 'ArrowDown':
        if (isShift && onPan) {
          onPan(0, -20);
        }
        break;
      
      case 'ArrowLeft':
        if (isShift && onPan) {
          onPan(20, 0);
        }
        break;
      
      case 'ArrowRight':
        if (isShift && onPan) {
          onPan(-20, 0);
        }
        break;
    }
  }, [
    enabled,
    onZoomIn,
    onZoomOut,
    onResetZoom,
    onFitToScreen,
    onToolChange,
    onUndo,
    onRedo,
    onSave,
    onDelete,
    onSelectAll,
    onDeselect,
    onCopy,
    onPaste,
    onDuplicate,
    onPan
  ]);

  const handleKeyUp = useCallback((e: KeyboardEvent) => {
    if (!enabled) return;

    // Handle space bar release for pan tool
    if (e.code === 'Space' && onToolChange) {
      // Return to previous tool (this would need to be tracked in state)
      onToolChange(CanvasTool.SELECT);
    }
  }, [enabled, onToolChange]);

  useEffect(() => {
    if (!enabled) return;

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, [handleKeyDown, handleKeyUp, enabled]);

  // Return shortcut information for help/documentation
  const shortcuts = {
    zoom: [
      { keys: 'Ctrl + +', description: 'Zoom in' },
      { keys: 'Ctrl + -', description: 'Zoom out' },
      { keys: 'Ctrl + 0', description: 'Reset zoom to 100%' },
      { keys: 'Ctrl + Shift + 0', description: 'Fit to screen' }
    ],
    tools: [
      { keys: 'V', description: 'Select tool' },
      { keys: 'T', description: 'Text region tool' },
      { keys: 'H or Space', description: 'Pan tool' }
    ],
    actions: [
      { keys: 'Ctrl + Z', description: 'Undo' },
      { keys: 'Ctrl + Y or Ctrl + Shift + Z', description: 'Redo' },
      { keys: 'Ctrl + S', description: 'Save' },
      { keys: 'Delete or Backspace', description: 'Delete selected' },
      { keys: 'Escape', description: 'Deselect all' }
    ],
    selection: [
      { keys: 'Ctrl + A', description: 'Select all' },
      { keys: 'Ctrl + C', description: 'Copy' },
      { keys: 'Ctrl + V', description: 'Paste' },
      { keys: 'Ctrl + D', description: 'Duplicate' }
    ],
    navigation: [
      { keys: 'Shift + Arrow Keys', description: 'Pan canvas' }
    ]
  };

  return { shortcuts };
};

export default useKeyboardShortcuts;
