'use client';

import { useCallback } from 'react';
import { useLoadingState, useErrorState } from '@/hooks/useAppState';
import { useErrorHandler } from '@/components/providers/ErrorProvider';
import { useToastHelpers } from '@/components/feedback/ToastNotification';

// Loading and error state manager
export const useLoadingError = () => {
  const { loading, setLoading } = useLoadingState();
  const { errors, setError, clearErrors } = useErrorState();
  const { addError } = useErrorHandler();
  const toast = useToastHelpers();

  // Set loading state for a specific operation
  const setOperationLoading = useCallback((operation: keyof typeof loading, isLoading: boolean) => {
    setLoading(operation, isLoading);
  }, [setLoading]);

  // Set error for a specific operation
  const setOperationError = useCallback((operation: keyof typeof errors, error: string | null) => {
    setError(operation, error);
    
    // Also add to global error handler if it's a new error
    if (error) {
      addError({
        title: `${operation} Error`,
        message: error,
        severity: 'error',
        source: operation
      });
    }
  }, [setError, addError]);

  // Handle async operation with automatic loading and error management
  const handleAsyncOperation = useCallback(async <T>(
    operation: keyof typeof loading,
    asyncFunction: () => Promise<T>,
    options: {
      successMessage?: string;
      errorMessage?: string;
      showSuccessToast?: boolean;
      showErrorToast?: boolean;
    } = {}
  ): Promise<T | null> => {
    const {
      successMessage = 'Operation completed successfully',
      errorMessage = 'Operation failed',
      showSuccessToast = false,
      showErrorToast = true
    } = options;

    try {
      // Set loading state
      setOperationLoading(operation, true);
      setOperationError(operation, null);

      // Execute async function
      const result = await asyncFunction();

      // Show success toast if requested
      if (showSuccessToast) {
        toast.success(successMessage);
      }

      return result;
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      
      // Set error state
      setOperationError(operation, errorMsg);

      // Show error toast if requested
      if (showErrorToast) {
        toast.error(`${errorMessage}: ${errorMsg}`);
      }

      return null;
    } finally {
      // Clear loading state
      setOperationLoading(operation, false);
    }
  }, [setOperationLoading, setOperationError, toast]);

  // Handle API calls with standard error handling
  const handleApiCall = useCallback(async <T>(
    operation: keyof typeof loading,
    apiCall: () => Promise<T>,
    options: {
      successMessage?: string;
      showSuccessToast?: boolean;
    } = {}
  ): Promise<T | null> => {
    return handleAsyncOperation(operation, apiCall, {
      errorMessage: 'API request failed',
      showErrorToast: true,
      ...options
    });
  }, [handleAsyncOperation]);

  // Handle file operations
  const handleFileOperation = useCallback(async <T>(
    operation: keyof typeof loading,
    fileOperation: () => Promise<T>,
    options: {
      successMessage?: string;
      showSuccessToast?: boolean;
    } = {}
  ): Promise<T | null> => {
    return handleAsyncOperation(operation, fileOperation, {
      successMessage: 'File operation completed',
      errorMessage: 'File operation failed',
      showSuccessToast: true,
      showErrorToast: true,
      ...options
    });
  }, [handleAsyncOperation]);

  // Handle form submissions
  const handleFormSubmission = useCallback(async <T>(
    operation: keyof typeof loading,
    submitFunction: () => Promise<T>,
    options: {
      successMessage?: string;
      showSuccessToast?: boolean;
    } = {}
  ): Promise<T | null> => {
    return handleAsyncOperation(operation, submitFunction, {
      successMessage: 'Form submitted successfully',
      errorMessage: 'Form submission failed',
      showSuccessToast: true,
      showErrorToast: true,
      ...options
    });
  }, [handleAsyncOperation]);

  // Check if any operation is loading
  const isAnyLoading = Object.values(loading).some(Boolean);

  // Check if any operation has errors
  const hasAnyErrors = Object.values(errors).some(Boolean);

  // Get all current errors
  const getAllErrors = useCallback(() => {
    return Object.entries(errors)
      .filter(([, error]) => error !== null)
      .map(([operation, error]) => ({ operation, error }));
  }, [errors]);

  // Clear all errors
  const clearAllErrors = useCallback(() => {
    clearErrors();
  }, [clearErrors]);

  // Retry operation (clears error and allows retry)
  const retryOperation = useCallback((operation: keyof typeof errors) => {
    setOperationError(operation, null);
  }, [setOperationError]);

  return {
    // State
    loading,
    errors,
    isAnyLoading,
    hasAnyErrors,

    // Actions
    setOperationLoading,
    setOperationError,
    handleAsyncOperation,
    handleApiCall,
    handleFileOperation,
    handleFormSubmission,
    getAllErrors,
    clearAllErrors,
    retryOperation
  };
};

// Hook for specific operation loading/error state
export const useOperationState = (operation: string) => {
  const { loading, errors, setOperationLoading, setOperationError, retryOperation } = useLoadingError();

  return {
    isLoading: loading[operation as keyof typeof loading] || false,
    error: errors[operation as keyof typeof errors] || null,
    setLoading: (isLoading: boolean) => setOperationLoading(operation as keyof typeof loading, isLoading),
    setError: (error: string | null) => setOperationError(operation as keyof typeof errors, error),
    retry: () => retryOperation(operation as keyof typeof errors),
    hasError: Boolean(errors[operation as keyof typeof errors])
  };
};

// Hook for project operations
export const useProjectOperations = () => {
  const { handleApiCall, handleFileOperation } = useLoadingError();

  const loadProjects = useCallback((apiCall: () => Promise<any>) => {
    return handleApiCall('projects', apiCall, {
      successMessage: 'Projects loaded successfully',
      showSuccessToast: false
    });
  }, [handleApiCall]);

  const createProject = useCallback((apiCall: () => Promise<any>) => {
    return handleApiCall('currentProject', apiCall, {
      successMessage: 'Project created successfully',
      showSuccessToast: true
    });
  }, [handleApiCall]);

  const uploadFile = useCallback((fileOperation: () => Promise<any>) => {
    return handleFileOperation('currentPage', fileOperation, {
      successMessage: 'File uploaded successfully',
      showSuccessToast: true
    });
  }, [handleFileOperation]);

  return {
    loadProjects,
    createProject,
    uploadFile
  };
};

// Hook for OCR operations
export const useOCROperations = () => {
  const { handleApiCall } = useLoadingError();

  const startOCR = useCallback((apiCall: () => Promise<any>) => {
    return handleApiCall('ocr', apiCall, {
      successMessage: 'OCR processing started',
      showSuccessToast: true
    });
  }, [handleApiCall]);

  return {
    startOCR
  };
};

// Hook for translation operations
export const useTranslationOperations = () => {
  const { handleApiCall } = useLoadingError();

  const startTranslation = useCallback((apiCall: () => Promise<any>) => {
    return handleApiCall('translation', apiCall, {
      successMessage: 'Translation started',
      showSuccessToast: true
    });
  }, [handleApiCall]);

  return {
    startTranslation
  };
};

export default useLoadingError;
