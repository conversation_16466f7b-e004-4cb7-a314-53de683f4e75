'use client';

import { useCallback, useEffect } from 'react';
import { useAppContext, AppAction } from '@/store/AppContext';
import { persistenceUtils, AutoSave } from '@/store/persistence';
import { ProjectResponse, ProjectPageResponse } from '@/types/api';
import { CanvasTextRegion, CanvasState, CanvasTool } from '@/types/canvas';

// Main app state hook
export const useAppState = () => {
  const { state, dispatch } = useAppContext();

  // Auto-save setup
  const autoSave = new AutoSave((changes) => {
    console.log('Auto-saving changes:', changes);
    // Save different parts of state based on what changed
    if (changes.includes('preferences')) {
      persistenceUtils.savePreferences(state.preferences);
    }
    if (changes.includes('ui')) {
      persistenceUtils.saveUIState(state.ui);
    }
    if (changes.includes('canvas')) {
      persistenceUtils.saveCanvasState(state.canvas);
    }
    if (changes.includes('textRegions') && state.currentPage) {
      persistenceUtils.saveTextRegions(state.currentPage.id, state.textRegions);
    }
  });

  // Load persisted state on mount
  useEffect(() => {
    const loadPersistedState = () => {
      // Load preferences
      const preferences = persistenceUtils.loadPreferences();
      if (preferences) {
        dispatch({ type: 'UPDATE_PREFERENCES', payload: preferences });
      }

      // Load UI state
      const uiState = persistenceUtils.loadUIState();
      if (uiState) {
        Object.entries(uiState).forEach(([key, value]) => {
          if (typeof value === 'boolean') {
            dispatch({
              type: 'SET_PANEL_VISIBILITY',
              payload: { panel: key as any, visible: value }
            });
          } else if (key === 'sidebarWidth') {
            dispatch({ type: 'SET_SIDEBAR_WIDTH', payload: value as number });
          } else if (key === 'rightPanelWidth') {
            dispatch({ type: 'SET_RIGHT_PANEL_WIDTH', payload: value as number });
          }
        });
      }

      // Load canvas state
      const canvasState = persistenceUtils.loadCanvasState();
      if (canvasState) {
        dispatch({ type: 'UPDATE_CANVAS_STATE', payload: canvasState });
      }
    };

    loadPersistedState();

    // Cleanup auto-save on unmount
    return () => {
      autoSave.stop();
    };
  }, [dispatch]);

  // Save state when it changes
  useEffect(() => {
    autoSave.markChanged('preferences');
  }, [state.preferences]);

  useEffect(() => {
    autoSave.markChanged('ui');
  }, [state.ui]);

  useEffect(() => {
    autoSave.markChanged('canvas');
  }, [state.canvas]);

  useEffect(() => {
    autoSave.markChanged('textRegions');
  }, [state.textRegions]);

  // Load text regions when page changes
  useEffect(() => {
    if (state.currentPage) {
      const savedRegions = persistenceUtils.loadTextRegions(state.currentPage.id);
      if (savedRegions) {
        dispatch({ type: 'SET_TEXT_REGIONS', payload: savedRegions });
      }
    }
  }, [state.currentPage?.id, dispatch]);

  return {
    state,
    dispatch,
    autoSave
  };
};

// Project management hook
export const useProjects = () => {
  const { state, dispatch } = useAppState();

  const setProjects = useCallback((projects: ProjectResponse[]) => {
    dispatch({ type: 'SET_PROJECTS', payload: projects });
  }, [dispatch]);

  const setCurrentProject = useCallback((project: ProjectResponse | null) => {
    dispatch({ type: 'SET_CURRENT_PROJECT', payload: project });
  }, [dispatch]);

  const setCurrentPage = useCallback((page: ProjectPageResponse | null) => {
    dispatch({ type: 'SET_CURRENT_PAGE', payload: page });
  }, [dispatch]);

  const addProject = useCallback((project: ProjectResponse) => {
    dispatch({ type: 'ADD_PROJECT', payload: project });
  }, [dispatch]);

  const updateProject = useCallback((id: string, updates: Partial<ProjectResponse>) => {
    dispatch({ type: 'UPDATE_PROJECT', payload: { id, updates } });
  }, [dispatch]);

  const deleteProject = useCallback((id: string) => {
    dispatch({ type: 'DELETE_PROJECT', payload: id });
  }, [dispatch]);

  return {
    projects: state.projects,
    currentProject: state.currentProject,
    currentPage: state.currentPage,
    setProjects,
    setCurrentProject,
    setCurrentPage,
    addProject,
    updateProject,
    deleteProject
  };
};

// Canvas state hook
export const useCanvasState = () => {
  const { state, dispatch } = useAppState();

  const updateCanvasState = useCallback((updates: Partial<CanvasState>) => {
    dispatch({ type: 'UPDATE_CANVAS_STATE', payload: updates });
  }, [dispatch]);

  const setTool = useCallback((tool: CanvasTool) => {
    dispatch({ type: 'SET_CANVAS_TOOL', payload: tool });
  }, [dispatch]);

  const setZoom = useCallback((zoom: number) => {
    dispatch({ type: 'SET_ZOOM', payload: zoom });
  }, [dispatch]);

  const setPan = useCallback((x: number, y: number) => {
    dispatch({ type: 'SET_PAN', payload: { x, y } });
  }, [dispatch]);

  const zoomIn = useCallback(() => {
    const newZoom = Math.min(state.canvas.zoom * 1.2, 5);
    setZoom(newZoom);
  }, [state.canvas.zoom, setZoom]);

  const zoomOut = useCallback(() => {
    const newZoom = Math.max(state.canvas.zoom / 1.2, 0.1);
    setZoom(newZoom);
  }, [state.canvas.zoom, setZoom]);

  const resetZoom = useCallback(() => {
    setZoom(1);
  }, [setZoom]);

  return {
    canvasState: state.canvas,
    updateCanvasState,
    setTool,
    setZoom,
    setPan,
    zoomIn,
    zoomOut,
    resetZoom
  };
};

// Text regions hook
export const useTextRegions = () => {
  const { state, dispatch } = useAppState();

  const setTextRegions = useCallback((regions: CanvasTextRegion[]) => {
    dispatch({ type: 'SET_TEXT_REGIONS', payload: regions });
  }, [dispatch]);

  const addTextRegion = useCallback((region: CanvasTextRegion) => {
    dispatch({ type: 'ADD_TEXT_REGION', payload: region });
  }, [dispatch]);

  const updateTextRegion = useCallback((id: string, updates: Partial<CanvasTextRegion>) => {
    dispatch({ type: 'UPDATE_TEXT_REGION', payload: { id, updates } });
  }, [dispatch]);

  const deleteTextRegion = useCallback((id: string) => {
    dispatch({ type: 'DELETE_TEXT_REGION', payload: id });
  }, [dispatch]);

  const selectTextRegions = useCallback((ids: string[]) => {
    dispatch({ type: 'SELECT_TEXT_REGIONS', payload: ids });
  }, [dispatch]);

  const deselectAllRegions = useCallback(() => {
    dispatch({ type: 'DESELECT_ALL_REGIONS' });
  }, [dispatch]);

  const getSelectedRegions = useCallback(() => {
    return state.textRegions.filter(region => state.selectedRegionIds.includes(region.id));
  }, [state.textRegions, state.selectedRegionIds]);

  return {
    textRegions: state.textRegions,
    selectedRegionIds: state.selectedRegionIds,
    setTextRegions,
    addTextRegion,
    updateTextRegion,
    deleteTextRegion,
    selectTextRegions,
    deselectAllRegions,
    getSelectedRegions
  };
};

// UI state hook
export const useUIState = () => {
  const { state, dispatch } = useAppState();

  const togglePanel = useCallback((panel: keyof typeof state.ui) => {
    dispatch({ type: 'TOGGLE_PANEL', payload: panel });
  }, [dispatch]);

  const setPanelVisibility = useCallback((panel: keyof typeof state.ui, visible: boolean) => {
    dispatch({ type: 'SET_PANEL_VISIBILITY', payload: { panel, visible } });
  }, [dispatch]);

  const setSidebarWidth = useCallback((width: number) => {
    dispatch({ type: 'SET_SIDEBAR_WIDTH', payload: width });
  }, [dispatch]);

  const setRightPanelWidth = useCallback((width: number) => {
    dispatch({ type: 'SET_RIGHT_PANEL_WIDTH', payload: width });
  }, [dispatch]);

  return {
    ui: state.ui,
    togglePanel,
    setPanelVisibility,
    setSidebarWidth,
    setRightPanelWidth
  };
};

// Preferences hook
export const usePreferences = () => {
  const { state, dispatch } = useAppState();

  const updatePreferences = useCallback((updates: Partial<typeof state.preferences>) => {
    dispatch({ type: 'UPDATE_PREFERENCES', payload: updates });
  }, [dispatch]);

  const resetPreferences = useCallback(() => {
    dispatch({ type: 'RESET_PREFERENCES' });
  }, [dispatch]);

  return {
    preferences: state.preferences,
    updatePreferences,
    resetPreferences
  };
};

// Loading state hook
export const useLoadingState = () => {
  const { state, dispatch } = useAppState();

  const setLoading = useCallback((key: keyof typeof state.loading, loading: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: { key, loading } });
  }, [dispatch]);

  return {
    loading: state.loading,
    setLoading
  };
};

// Error state hook
export const useErrorState = () => {
  const { state, dispatch } = useAppState();

  const setError = useCallback((key: keyof typeof state.errors, error: string | null) => {
    dispatch({ type: 'SET_ERROR', payload: { key, error } });
  }, [dispatch]);

  const clearErrors = useCallback(() => {
    dispatch({ type: 'CLEAR_ERRORS' });
  }, [dispatch]);

  return {
    errors: state.errors,
    setError,
    clearErrors
  };
};

// History hook for undo/redo
export const useHistory = () => {
  const { state, dispatch } = useAppState();

  const undo = useCallback(() => {
    dispatch({ type: 'UNDO' });
  }, [dispatch]);

  const redo = useCallback(() => {
    dispatch({ type: 'REDO' });
  }, [dispatch]);

  const clearHistory = useCallback(() => {
    dispatch({ type: 'CLEAR_HISTORY' });
  }, [dispatch]);

  const canUndo = state.history.past.length > 0;
  const canRedo = state.history.future.length > 0;

  return {
    history: state.history,
    undo,
    redo,
    clearHistory,
    canUndo,
    canRedo
  };
};

export default useAppState;
