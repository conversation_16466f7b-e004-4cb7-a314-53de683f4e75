import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AppProvider, useAppContext } from '@/store/AppContext';
import { useProjects, useCanvasState, useTextRegions } from '@/hooks/useAppState';
import { persistenceUtils } from '@/store/persistence';
import { CanvasTool } from '@/types/canvas';

// Mock persistence utils
jest.mock('@/store/persistence', () => ({
  persistenceUtils: {
    savePreferences: jest.fn(),
    loadPreferences: jest.fn(),
    saveUIState: jest.fn(),
    loadUIState: jest.fn(),
    saveCanvasState: jest.fn(),
    loadCanvasState: jest.fn(),
    saveTextRegions: jest.fn(),
    loadTextRegions: jest.fn(),
  }
}));

// Test component for projects
const ProjectTestComponent: React.FC = () => {
  const {
    projects,
    currentProject,
    setProjects,
    setCurrentProject,
    addProject,
    updateProject,
    deleteProject
  } = useProjects();

  return (
    <div>
      <div data-testid="project-count">{projects.length}</div>
      <div data-testid="current-project">{currentProject?.name || 'None'}</div>
      
      <button
        onClick={() => setProjects([
          { id: '1', name: 'Project 1', description: '', source_language: 'ja', target_language: 'en', created_at: '', updated_at: '', page_count: 0, status: 'active' }
        ])}
        data-testid="set-projects"
      >
        Set Projects
      </button>
      
      <button
        onClick={() => setCurrentProject(projects[0] || null)}
        data-testid="set-current-project"
      >
        Set Current Project
      </button>
      
      <button
        onClick={() => addProject({
          id: '2', name: 'Project 2', description: '', source_language: 'ja', target_language: 'en', created_at: '', updated_at: '', page_count: 0, status: 'active'
        })}
        data-testid="add-project"
      >
        Add Project
      </button>
      
      <button
        onClick={() => updateProject('1', { name: 'Updated Project 1' })}
        data-testid="update-project"
      >
        Update Project
      </button>
      
      <button
        onClick={() => deleteProject('1')}
        data-testid="delete-project"
      >
        Delete Project
      </button>
    </div>
  );
};

// Test component for canvas state
const CanvasTestComponent: React.FC = () => {
  const {
    canvasState,
    setTool,
    setZoom,
    setPan,
    zoomIn,
    zoomOut,
    resetZoom
  } = useCanvasState();

  return (
    <div>
      <div data-testid="canvas-tool">{canvasState.selectedTool}</div>
      <div data-testid="canvas-zoom">{canvasState.zoom}</div>
      <div data-testid="canvas-pan">{canvasState.panX},{canvasState.panY}</div>
      
      <button onClick={() => setTool(CanvasTool.TEXT_REGION)} data-testid="set-text-tool">
        Set Text Tool
      </button>
      
      <button onClick={() => setZoom(2)} data-testid="set-zoom">
        Set Zoom 2x
      </button>
      
      <button onClick={() => setPan(100, 200)} data-testid="set-pan">
        Set Pan
      </button>
      
      <button onClick={zoomIn} data-testid="zoom-in">
        Zoom In
      </button>
      
      <button onClick={zoomOut} data-testid="zoom-out">
        Zoom Out
      </button>
      
      <button onClick={resetZoom} data-testid="reset-zoom">
        Reset Zoom
      </button>
    </div>
  );
};

// Test component for text regions
const TextRegionsTestComponent: React.FC = () => {
  const {
    textRegions,
    selectedRegionIds,
    addTextRegion,
    updateTextRegion,
    deleteTextRegion,
    selectTextRegions,
    deselectAllRegions,
    getSelectedRegions
  } = useTextRegions();

  const selectedRegions = getSelectedRegions();

  return (
    <div>
      <div data-testid="region-count">{textRegions.length}</div>
      <div data-testid="selected-count">{selectedRegionIds.length}</div>
      <div data-testid="selected-regions-count">{selectedRegions.length}</div>
      
      <button
        onClick={() => addTextRegion({
          id: 'region-1',
          x: 0.1,
          y: 0.1,
          width: 0.2,
          height: 0.1,
          original_text: 'Test text',
          translated_text: '',
          region_type: 'speech_bubble',
          translation_status: 'pending',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          isSelected: false
        })}
        data-testid="add-region"
      >
        Add Region
      </button>
      
      <button
        onClick={() => updateTextRegion('region-1', { translated_text: 'Translated text' })}
        data-testid="update-region"
      >
        Update Region
      </button>
      
      <button
        onClick={() => selectTextRegions(['region-1'])}
        data-testid="select-region"
      >
        Select Region
      </button>
      
      <button
        onClick={() => deselectAllRegions()}
        data-testid="deselect-all"
      >
        Deselect All
      </button>
      
      <button
        onClick={() => deleteTextRegion('region-1')}
        data-testid="delete-region"
      >
        Delete Region
      </button>
    </div>
  );
};

describe('State Management Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Project State Management', () => {
    it('should manage projects state correctly', async () => {
      render(
        <AppProvider>
          <ProjectTestComponent />
        </AppProvider>
      );

      // Initial state
      expect(screen.getByTestId('project-count')).toHaveTextContent('0');
      expect(screen.getByTestId('current-project')).toHaveTextContent('None');

      // Set projects
      fireEvent.click(screen.getByTestId('set-projects'));
      await waitFor(() => {
        expect(screen.getByTestId('project-count')).toHaveTextContent('1');
      });

      // Set current project
      fireEvent.click(screen.getByTestId('set-current-project'));
      await waitFor(() => {
        expect(screen.getByTestId('current-project')).toHaveTextContent('Project 1');
      });

      // Add project
      fireEvent.click(screen.getByTestId('add-project'));
      await waitFor(() => {
        expect(screen.getByTestId('project-count')).toHaveTextContent('2');
      });

      // Update project
      fireEvent.click(screen.getByTestId('update-project'));
      await waitFor(() => {
        expect(screen.getByTestId('current-project')).toHaveTextContent('Updated Project 1');
      });

      // Delete project
      fireEvent.click(screen.getByTestId('delete-project'));
      await waitFor(() => {
        expect(screen.getByTestId('project-count')).toHaveTextContent('1');
        expect(screen.getByTestId('current-project')).toHaveTextContent('None');
      });
    });
  });

  describe('Canvas State Management', () => {
    it('should manage canvas state correctly', async () => {
      render(
        <AppProvider>
          <CanvasTestComponent />
        </AppProvider>
      );

      // Initial state
      expect(screen.getByTestId('canvas-tool')).toHaveTextContent(CanvasTool.SELECT);
      expect(screen.getByTestId('canvas-zoom')).toHaveTextContent('1');
      expect(screen.getByTestId('canvas-pan')).toHaveTextContent('0,0');

      // Set tool
      fireEvent.click(screen.getByTestId('set-text-tool'));
      await waitFor(() => {
        expect(screen.getByTestId('canvas-tool')).toHaveTextContent(CanvasTool.TEXT_REGION);
      });

      // Set zoom
      fireEvent.click(screen.getByTestId('set-zoom'));
      await waitFor(() => {
        expect(screen.getByTestId('canvas-zoom')).toHaveTextContent('2');
      });

      // Set pan
      fireEvent.click(screen.getByTestId('set-pan'));
      await waitFor(() => {
        expect(screen.getByTestId('canvas-pan')).toHaveTextContent('100,200');
      });

      // Zoom in
      fireEvent.click(screen.getByTestId('zoom-in'));
      await waitFor(() => {
        expect(parseFloat(screen.getByTestId('canvas-zoom').textContent!)).toBeGreaterThan(2);
      });

      // Reset zoom
      fireEvent.click(screen.getByTestId('reset-zoom'));
      await waitFor(() => {
        expect(screen.getByTestId('canvas-zoom')).toHaveTextContent('1');
      });
    });
  });

  describe('Text Regions State Management', () => {
    it('should manage text regions state correctly', async () => {
      render(
        <AppProvider>
          <TextRegionsTestComponent />
        </AppProvider>
      );

      // Initial state
      expect(screen.getByTestId('region-count')).toHaveTextContent('0');
      expect(screen.getByTestId('selected-count')).toHaveTextContent('0');

      // Add region
      fireEvent.click(screen.getByTestId('add-region'));
      await waitFor(() => {
        expect(screen.getByTestId('region-count')).toHaveTextContent('1');
      });

      // Select region
      fireEvent.click(screen.getByTestId('select-region'));
      await waitFor(() => {
        expect(screen.getByTestId('selected-count')).toHaveTextContent('1');
        expect(screen.getByTestId('selected-regions-count')).toHaveTextContent('1');
      });

      // Update region
      fireEvent.click(screen.getByTestId('update-region'));
      // Note: We can't easily test the update without exposing the region data

      // Deselect all
      fireEvent.click(screen.getByTestId('deselect-all'));
      await waitFor(() => {
        expect(screen.getByTestId('selected-count')).toHaveTextContent('0');
      });

      // Delete region
      fireEvent.click(screen.getByTestId('delete-region'));
      await waitFor(() => {
        expect(screen.getByTestId('region-count')).toHaveTextContent('0');
      });
    });
  });

  describe('Persistence Integration', () => {
    it('should call persistence functions when state changes', async () => {
      const TestComponent: React.FC = () => {
        const { dispatch } = useAppContext();
        
        return (
          <button
            onClick={() => dispatch({ type: 'UPDATE_PREFERENCES', payload: { theme: 'dark' } })}
            data-testid="update-preferences"
          >
            Update Preferences
          </button>
        );
      };

      render(
        <AppProvider>
          <TestComponent />
        </AppProvider>
      );

      fireEvent.click(screen.getByTestId('update-preferences'));

      // Note: In a real test, we would need to wait for the auto-save debounce
      // and verify that persistence functions are called
      await waitFor(() => {
        // This would need to be implemented with proper timing
        // expect(persistenceUtils.savePreferences).toHaveBeenCalled();
      });
    });
  });
});
