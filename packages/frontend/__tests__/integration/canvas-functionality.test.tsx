import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AppProvider } from '@/store/AppContext';
import { CanvasEditor } from '@/components/canvas/CanvasEditor';
import { CanvasTool } from '@/types/canvas';

// Mock canvas context
const mockCanvasContext = {
  fillRect: jest.fn(),
  clearRect: jest.fn(),
  strokeRect: jest.fn(),
  beginPath: jest.fn(),
  moveTo: jest.fn(),
  lineTo: jest.fn(),
  stroke: jest.fn(),
  fill: jest.fn(),
  save: jest.fn(),
  restore: jest.fn(),
  translate: jest.fn(),
  scale: jest.fn(),
  setTransform: jest.fn(),
  drawImage: jest.fn(),
  getImageData: jest.fn(() => ({ data: new Uint8ClampedArray(4) })),
  putImageData: jest.fn(),
  measureText: jest.fn(() => ({ width: 100 })),
  fillText: jest.fn(),
  strokeText: jest.fn(),
  arc: jest.fn(),
  rect: jest.fn(),
  clip: jest.fn(),
  globalAlpha: 1,
  fillStyle: '#000000',
  strokeStyle: '#000000',
  lineWidth: 1,
  font: '14px Arial',
  textAlign: 'left' as CanvasTextAlign,
  textBaseline: 'top' as CanvasTextBaseline,
};

// Mock HTMLCanvasElement
HTMLCanvasElement.prototype.getContext = jest.fn(() => mockCanvasContext);
HTMLCanvasElement.prototype.getBoundingClientRect = jest.fn(() => ({
  left: 0,
  top: 0,
  width: 800,
  height: 600,
  right: 800,
  bottom: 600,
  x: 0,
  y: 0,
  toJSON: () => ({})
}));

// Mock image loading
const mockImage = {
  onload: null as any,
  onerror: null as any,
  src: '',
  width: 800,
  height: 600,
  naturalWidth: 800,
  naturalHeight: 600,
  complete: true
};

global.Image = jest.fn(() => mockImage) as any;

describe('Canvas Functionality Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const mockImageInfo = {
    originalWidth: 800,
    originalHeight: 600,
    displayWidth: 800,
    displayHeight: 600,
    aspectRatio: 800 / 600
  };

  const mockTextRegions = [
    {
      id: 'region-1',
      x: 0.1,
      y: 0.1,
      width: 0.2,
      height: 0.1,
      original_text: 'Hello',
      translated_text: 'こんにちは',
      region_type: 'speech_bubble' as const,
      translation_status: 'completed' as const,
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
      isSelected: false
    },
    {
      id: 'region-2',
      x: 0.3,
      y: 0.3,
      width: 0.25,
      height: 0.12,
      original_text: 'World',
      translated_text: '世界',
      region_type: 'narration' as const,
      translation_status: 'completed' as const,
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
      isSelected: false
    }
  ];

  it('should render canvas with image and text regions', async () => {
    render(
      <AppProvider>
        <CanvasEditor
          imageInfo={mockImageInfo}
          textRegions={mockTextRegions}
          onRegionUpdate={jest.fn()}
          onRegionSelect={jest.fn()}
          onRegionCreate={jest.fn()}
          onRegionDelete={jest.fn()}
          className="test-canvas"
        />
      </AppProvider>
    );

    const canvas = screen.getByRole('img', { hidden: true }) || screen.getByTestId('canvas');
    expect(canvas).toBeInTheDocument();
  });

  it('should handle canvas tool switching', async () => {
    const onToolChange = jest.fn();
    
    render(
      <AppProvider>
        <div>
          <button onClick={() => onToolChange(CanvasTool.SELECT)} data-testid="select-tool">
            Select
          </button>
          <button onClick={() => onToolChange(CanvasTool.TEXT_REGION)} data-testid="text-tool">
            Text Region
          </button>
          <button onClick={() => onToolChange(CanvasTool.PAN)} data-testid="pan-tool">
            Pan
          </button>
        </div>
      </AppProvider>
    );

    fireEvent.click(screen.getByTestId('text-tool'));
    expect(onToolChange).toHaveBeenCalledWith(CanvasTool.TEXT_REGION);

    fireEvent.click(screen.getByTestId('pan-tool'));
    expect(onToolChange).toHaveBeenCalledWith(CanvasTool.PAN);

    fireEvent.click(screen.getByTestId('select-tool'));
    expect(onToolChange).toHaveBeenCalledWith(CanvasTool.SELECT);
  });

  it('should handle zoom operations', async () => {
    const onZoomChange = jest.fn();
    
    render(
      <AppProvider>
        <div>
          <button onClick={() => onZoomChange(2)} data-testid="zoom-in">
            Zoom In
          </button>
          <button onClick={() => onZoomChange(0.5)} data-testid="zoom-out">
            Zoom Out
          </button>
          <button onClick={() => onZoomChange(1)} data-testid="reset-zoom">
            Reset Zoom
          </button>
        </div>
      </AppProvider>
    );

    fireEvent.click(screen.getByTestId('zoom-in'));
    expect(onZoomChange).toHaveBeenCalledWith(2);

    fireEvent.click(screen.getByTestId('zoom-out'));
    expect(onZoomChange).toHaveBeenCalledWith(0.5);

    fireEvent.click(screen.getByTestId('reset-zoom'));
    expect(onZoomChange).toHaveBeenCalledWith(1);
  });

  it('should handle text region creation', async () => {
    const onRegionCreate = jest.fn();
    
    const TestCanvas: React.FC = () => {
      const handleMouseDown = (e: React.MouseEvent) => {
        const rect = (e.target as HTMLElement).getBoundingClientRect();
        const x = (e.clientX - rect.left) / rect.width;
        const y = (e.clientY - rect.top) / rect.height;
        
        onRegionCreate({
          x,
          y,
          width: 0.1,
          height: 0.05,
          original_text: '',
          region_type: 'speech_bubble'
        });
      };

      return (
        <canvas
          data-testid="test-canvas"
          width={800}
          height={600}
          onMouseDown={handleMouseDown}
          style={{ border: '1px solid black' }}
        />
      );
    };

    render(
      <AppProvider>
        <TestCanvas />
      </AppProvider>
    );

    const canvas = screen.getByTestId('test-canvas');
    
    fireEvent.mouseDown(canvas, {
      clientX: 100,
      clientY: 100,
      bubbles: true
    });

    expect(onRegionCreate).toHaveBeenCalledWith(
      expect.objectContaining({
        x: expect.any(Number),
        y: expect.any(Number),
        width: 0.1,
        height: 0.05,
        original_text: '',
        region_type: 'speech_bubble'
      })
    );
  });

  it('should handle text region selection', async () => {
    const onRegionSelect = jest.fn();
    
    const TestRegionList: React.FC = () => {
      return (
        <div>
          {mockTextRegions.map((region) => (
            <div
              key={region.id}
              data-testid={`region-${region.id}`}
              onClick={() => onRegionSelect(region.id)}
              style={{
                border: region.isSelected ? '2px solid blue' : '1px solid gray',
                padding: '8px',
                margin: '4px',
                cursor: 'pointer'
              }}
            >
              {region.original_text} → {region.translated_text}
            </div>
          ))}
        </div>
      );
    };

    render(
      <AppProvider>
        <TestRegionList />
      </AppProvider>
    );

    fireEvent.click(screen.getByTestId('region-region-1'));
    expect(onRegionSelect).toHaveBeenCalledWith('region-1');

    fireEvent.click(screen.getByTestId('region-region-2'));
    expect(onRegionSelect).toHaveBeenCalledWith('region-2');
  });

  it('should handle text region updates', async () => {
    const onRegionUpdate = jest.fn();
    
    const TestRegionEditor: React.FC = () => {
      const [text, setText] = React.useState('');

      const handleUpdate = () => {
        onRegionUpdate('region-1', {
          translated_text: text,
          translation_status: 'completed' as const
        });
      };

      return (
        <div>
          <input
            data-testid="text-input"
            value={text}
            onChange={(e) => setText(e.target.value)}
            placeholder="Enter translation"
          />
          <button onClick={handleUpdate} data-testid="update-button">
            Update
          </button>
        </div>
      );
    };

    render(
      <AppProvider>
        <TestRegionEditor />
      </AppProvider>
    );

    const input = screen.getByTestId('text-input');
    const updateButton = screen.getByTestId('update-button');

    fireEvent.change(input, { target: { value: 'Updated translation' } });
    fireEvent.click(updateButton);

    expect(onRegionUpdate).toHaveBeenCalledWith('region-1', {
      translated_text: 'Updated translation',
      translation_status: 'completed'
    });
  });

  it('should handle keyboard shortcuts', async () => {
    const onToolChange = jest.fn();
    const onZoom = jest.fn();
    
    const TestKeyboardHandler: React.FC = () => {
      React.useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
          switch (e.key) {
            case 'v':
              onToolChange(CanvasTool.SELECT);
              break;
            case 't':
              onToolChange(CanvasTool.TEXT_REGION);
              break;
            case '=':
              if (e.ctrlKey) onZoom('in');
              break;
            case '-':
              if (e.ctrlKey) onZoom('out');
              break;
            case '0':
              if (e.ctrlKey) onZoom('reset');
              break;
          }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
      }, []);

      return <div data-testid="keyboard-handler">Keyboard Handler Active</div>;
    };

    render(
      <AppProvider>
        <TestKeyboardHandler />
      </AppProvider>
    );

    // Test tool shortcuts
    fireEvent.keyDown(document, { key: 'v' });
    expect(onToolChange).toHaveBeenCalledWith(CanvasTool.SELECT);

    fireEvent.keyDown(document, { key: 't' });
    expect(onToolChange).toHaveBeenCalledWith(CanvasTool.TEXT_REGION);

    // Test zoom shortcuts
    fireEvent.keyDown(document, { key: '=', ctrlKey: true });
    expect(onZoom).toHaveBeenCalledWith('in');

    fireEvent.keyDown(document, { key: '-', ctrlKey: true });
    expect(onZoom).toHaveBeenCalledWith('out');

    fireEvent.keyDown(document, { key: '0', ctrlKey: true });
    expect(onZoom).toHaveBeenCalledWith('reset');
  });

  it('should handle canvas pan operations', async () => {
    const onPanChange = jest.fn();
    
    const TestPanHandler: React.FC = () => {
      const [isPanning, setIsPanning] = React.useState(false);
      const [lastPos, setLastPos] = React.useState({ x: 0, y: 0 });

      const handleMouseDown = (e: React.MouseEvent) => {
        setIsPanning(true);
        setLastPos({ x: e.clientX, y: e.clientY });
      };

      const handleMouseMove = (e: React.MouseEvent) => {
        if (!isPanning) return;
        
        const deltaX = e.clientX - lastPos.x;
        const deltaY = e.clientY - lastPos.y;
        
        onPanChange(deltaX, deltaY);
        setLastPos({ x: e.clientX, y: e.clientY });
      };

      const handleMouseUp = () => {
        setIsPanning(false);
      };

      return (
        <div
          data-testid="pan-area"
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          style={{ width: 400, height: 300, border: '1px solid black' }}
        >
          Pan Area
        </div>
      );
    };

    render(
      <AppProvider>
        <TestPanHandler />
      </AppProvider>
    );

    const panArea = screen.getByTestId('pan-area');

    fireEvent.mouseDown(panArea, { clientX: 100, clientY: 100 });
    fireEvent.mouseMove(panArea, { clientX: 150, clientY: 120 });
    
    expect(onPanChange).toHaveBeenCalledWith(50, 20);

    fireEvent.mouseUp(panArea);
  });
});
