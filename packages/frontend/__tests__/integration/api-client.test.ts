import { projectsAP<PERSON>, ocrAPI, translationAPI } from '@/lib/api-client';
import {
  ProjectResponse,
  ProjectPageResponse,
  OCRJobResponse,
  TranslationJobResponse,
  ProjectStatus,
  OCRStatus,
  TranslationStatus,
  LLMProvider
} from '@/types/api';

// Mock fetch
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;

describe('API Client Integration Tests', () => {
  beforeEach(() => {
    mockFetch.mockClear();
  });



  describe('Project API', () => {
    const mockProject: ProjectResponse = {
      id: 'project-1',
      name: 'Test Project',
      description: 'Test Description',
      source_language: 'ja',
      target_language: 'en',
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
      page_count: 1,
      status: ProjectStatus.IN_PROGRESS
    };

    const mockPage: ProjectPageResponse = {
      id: 'page-1',
      project_id: 'project-1',
      page_number: 1,
      original_filename: 'test.jpg',
      file_path: '/uploads/test.jpg',
      file_size: 1024000,
      image_width: 800,
      image_height: 1200,
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
      ocr_status: OCRStatus.PENDING,
      text_region_count: 0
    };

    it('should get all projects', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => [mockProject],
      } as Response);

      const projects = await projectsAPI.getProjects();

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/projects'),
        expect.objectContaining({ method: 'GET' })
      );
      expect(projects).toEqual([mockProject]);
    });

    it('should create a new project', async () => {
      const createData = {
        name: 'New Project',
        description: 'New Description',
        source_language: 'ja',
        target_language: 'en'
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 201,
        json: async () => mockProject,
      } as Response);

      const project = await projectsAPI.createProject(createData);

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/projects'),
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(createData),
        })
      );
      expect(project).toEqual(mockProject);
    });

    it('should upload a page to project', async () => {
      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 201,
        json: async () => mockPage,
      } as Response);

      const page = await projectsAPI.uploadPage('project-1', file);

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/projects/project-1/pages'),
        expect.objectContaining({
          method: 'POST',
          body: expect.any(FormData),
        })
      );
      expect(page).toEqual(mockPage);
    });

    it('should get project pages', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => [mockPage],
      } as Response);

      const pages = await projectsAPI.getProjectPages('project-1');

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/projects/project-1/pages'),
        expect.objectContaining({ method: 'GET' })
      );
      expect(pages).toEqual([mockPage]);
    });
  });

  describe('OCR API', () => {
    const mockOCRJob: OCRJobResponse = {
      id: 'ocr-1',
      page_id: 'page-1',
      provider: 'tesseract',
      status: 'completed',
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
      processing_time: 5.2,
      result_count: 3
    };

    it('should start OCR processing', async () => {
      const ocrData = {
        page_id: 'page-1',
        provider: 'tesseract' as const,
        language: 'ja'
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 201,
        json: async () => mockOCRJob,
      } as Response);

      const job = await ocrAPI.startOCR(ocrData);

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/ocr/jobs'),
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(ocrData),
        })
      );
      expect(job).toEqual(mockOCRJob);
    });

    it('should get OCR job status', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => mockOCRJob,
      } as Response);

      const job = await ocrAPI.getOCRJobStatus('ocr-1');

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/ocr/jobs/ocr-1'),
        expect.objectContaining({ method: 'GET' })
      );
      expect(job).toEqual(mockOCRJob);
    });
  });

  describe('Translation API', () => {
    const mockTranslationJob: TranslationJobResponse = {
      id: 'translation-1',
      region_id: 'region-1',
      provider: 'claude',
      status: 'completed',
      source_language: 'ja',
      target_language: 'en',
      original_text: 'こんにちは',
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
      processing_time: 2.1,
      alternative_count: 2
    };

    it('should start translation', async () => {
      const translationData = {
        region_id: 'region-1',
        provider: 'claude' as const,
        source_language: 'ja',
        target_language: 'en',
        text: 'こんにちは'
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 201,
        json: async () => mockTranslationJob,
      } as Response);

      const job = await translationAPI.startTranslation(translationData);

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/translation/jobs'),
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(translationData),
        })
      );
      expect(job).toEqual(mockTranslationJob);
    });

    it('should get translation job status', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => mockTranslationJob,
      } as Response);

      const job = await translationAPI.getTranslationJobStatus('translation-1');

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/translation/jobs/translation-1'),
        expect.objectContaining({ method: 'GET' })
      );
      expect(job).toEqual(mockTranslationJob);
    });
  });

  describe('Error Handling', () => {
    it('should handle 401 unauthorized errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: async () => ({ error: 'Unauthorized' }),
      } as Response);

      await expect(projectsAPI.getProjects()).rejects.toThrow('API Error (401)');
    });

    it('should handle 500 server errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: async () => ({ error: 'Internal Server Error' }),
      } as Response);

      await expect(projectsAPI.getProjects()).rejects.toThrow('API Error (500)');
    });

    it('should handle malformed JSON responses', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => { throw new Error('Invalid JSON'); },
      } as Response);

      await expect(projectsAPI.getProjects()).rejects.toThrow('Invalid JSON');
    });
  });
});
