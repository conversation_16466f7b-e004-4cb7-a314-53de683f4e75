import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { AppProvider } from '@/store/AppContext';
import { StateProvider } from '@/components/providers/StateProvider';

// Mock API calls
const mockApiCalls = {
  createProject: jest.fn(),
  uploadPage: jest.fn(),
  startOCR: jest.fn(),
  startTranslation: jest.fn(),
  getProjects: jest.fn(),
  getProjectPages: jest.fn(),
};

// Mock fetch
global.fetch = jest.fn(() => Promise.resolve({
  ok: true,
  json: () => Promise.resolve({}),
  text: () => Promise.resolve(''),
  preconnect: jest.fn()
})) as jest.MockedFunction<typeof fetch>;

// Mock file upload
const mockFile = new File(['test content'], 'test-image.jpg', { type: 'image/jpeg' });

// Test component that simulates the main application workflow
const WorkflowTestApp: React.FC = () => {
  const [currentStep, setCurrentStep] = React.useState('project-creation');
  const [project, setProject] = React.useState<any>(null);
  const [page, setPage] = React.useState<any>(null);
  const [textRegions, setTextRegions] = React.useState<any[]>([]);

  const handleCreateProject = async () => {
    try {
      const newProject = {
        id: 'project-1',
        name: 'Test Manga Project',
        description: 'A test manga translation project',
        source_language: 'ja',
        target_language: 'en',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        page_count: 0,
        status: 'active'
      };

      mockApiCalls.createProject.mockResolvedValue(newProject);
      const result = await mockApiCalls.createProject(newProject);

      setProject(result);
      setCurrentStep('file-upload');
    } catch (error) {
      console.error('Failed to create project:', error);
    }
  };

  const handleFileUpload = async () => {
    try {
      const newPage = {
        id: 'page-1',
        project_id: project.id,
        page_number: 1,
        image_url: 'http://example.com/test-image.jpg',
        image_width: 800,
        image_height: 1200,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        ocr_status: 'pending',
        translation_status: 'pending'
      };

      mockApiCalls.uploadPage.mockResolvedValue(newPage);
      const result = await mockApiCalls.uploadPage(project.id, mockFile);

      setPage(result);
      setCurrentStep('ocr-processing');
    } catch (error) {
      console.error('Failed to upload file:', error);
    }
  };

  const handleStartOCR = async () => {
    try {
      const ocrJob = {
        id: 'ocr-1',
        page_id: page.id,
        provider: 'tesseract',
        status: 'completed',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        processing_time: 5.2,
        result_count: 2
      };

      mockApiCalls.startOCR.mockResolvedValue(ocrJob);
      await mockApiCalls.startOCR({
        page_id: page.id,
        provider: 'tesseract',
        language: 'ja'
      });

      // Simulate OCR results
      const newRegions = [
        {
          id: 'region-1',
          x: 0.1,
          y: 0.1,
          width: 0.2,
          height: 0.1,
          original_text: 'こんにちは',
          translated_text: '',
          region_type: 'speech_bubble',
          translation_status: 'pending',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          isSelected: false
        },
        {
          id: 'region-2',
          x: 0.3,
          y: 0.3,
          width: 0.25,
          height: 0.12,
          original_text: '世界',
          translated_text: '',
          region_type: 'narration',
          translation_status: 'pending',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          isSelected: false
        }
      ];

      setTextRegions(newRegions);
      setCurrentStep('translation');
    } catch (error) {
      console.error('Failed to start OCR:', error);
    }
  };

  const handleStartTranslation = async (regionId: string) => {
    try {
      const translationJob = {
        id: 'translation-1',
        region_id: regionId,
        provider: 'claude',
        status: 'completed',
        source_language: 'ja',
        target_language: 'en',
        original_text: textRegions.find(r => r.id === regionId)?.original_text,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        processing_time: 2.1,
        alternative_count: 2
      };

      mockApiCalls.startTranslation.mockResolvedValue(translationJob);
      await mockApiCalls.startTranslation({
        region_id: regionId,
        provider: 'claude',
        source_language: 'ja',
        target_language: 'en',
        text: textRegions.find(r => r.id === regionId)?.original_text
      });

      // Update region with translation
      setTextRegions(prev => prev.map(region =>
        region.id === regionId
          ? {
            ...region,
            translated_text: regionId === 'region-1' ? 'Hello' : 'World',
            translation_status: 'completed'
          }
          : region
      ));

      if (textRegions.every(r => r.id === regionId || r.translation_status === 'completed')) {
        setCurrentStep('completed');
      }
    } catch (error) {
      console.error('Failed to start translation:', error);
    }
  };

  return (
    <div data-testid="workflow-app">
      <div data-testid="current-step">{currentStep}</div>

      {currentStep === 'project-creation' && (
        <div data-testid="project-creation-step">
          <h2>Create New Project</h2>
          <button onClick={handleCreateProject} data-testid="create-project-btn">
            Create Project
          </button>
        </div>
      )}

      {currentStep === 'file-upload' && (
        <div data-testid="file-upload-step">
          <h2>Upload Manga Page</h2>
          <p>Project: {project?.name}</p>
          <button onClick={handleFileUpload} data-testid="upload-file-btn">
            Upload File
          </button>
        </div>
      )}

      {currentStep === 'ocr-processing' && (
        <div data-testid="ocr-step">
          <h2>OCR Processing</h2>
          <p>Page: {page?.page_number}</p>
          <button onClick={handleStartOCR} data-testid="start-ocr-btn">
            Start OCR
          </button>
        </div>
      )}

      {currentStep === 'translation' && (
        <div data-testid="translation-step">
          <h2>Translation</h2>
          <div data-testid="text-regions">
            {textRegions.map((region) => (
              <div key={region.id} data-testid={`region-${region.id}`}>
                <p>Original: {region.original_text}</p>
                <p>Translated: {region.translated_text || 'Not translated'}</p>
                <p>Status: {region.translation_status}</p>
                {region.translation_status === 'pending' && (
                  <button
                    onClick={() => handleStartTranslation(region.id)}
                    data-testid={`translate-${region.id}`}
                  >
                    Translate
                  </button>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {currentStep === 'completed' && (
        <div data-testid="completed-step">
          <h2>Translation Complete!</h2>
          <p>All text regions have been translated.</p>
        </div>
      )}
    </div>
  );
};

describe('User Workflow Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should complete the full manga translation workflow', async () => {
    const user = userEvent.setup();

    render(
      <StateProvider>
        <WorkflowTestApp />
      </StateProvider>
    );

    // Step 1: Project Creation
    expect(screen.getByTestId('current-step')).toHaveTextContent('project-creation');
    expect(screen.getByTestId('project-creation-step')).toBeInTheDocument();

    await user.click(screen.getByTestId('create-project-btn'));

    await waitFor(() => {
      expect(screen.getByTestId('current-step')).toHaveTextContent('file-upload');
    });
    expect(mockApiCalls.createProject).toHaveBeenCalled();

    // Step 2: File Upload
    expect(screen.getByTestId('file-upload-step')).toBeInTheDocument();
    expect(screen.getByText('Project: Test Manga Project')).toBeInTheDocument();

    await user.click(screen.getByTestId('upload-file-btn'));

    await waitFor(() => {
      expect(screen.getByTestId('current-step')).toHaveTextContent('ocr-processing');
    });
    expect(mockApiCalls.uploadPage).toHaveBeenCalled();

    // Step 3: OCR Processing
    expect(screen.getByTestId('ocr-step')).toBeInTheDocument();
    expect(screen.getByText('Page: 1')).toBeInTheDocument();

    await user.click(screen.getByTestId('start-ocr-btn'));

    await waitFor(() => {
      expect(screen.getByTestId('current-step')).toHaveTextContent('translation');
    });
    expect(mockApiCalls.startOCR).toHaveBeenCalled();

    // Step 4: Translation
    expect(screen.getByTestId('translation-step')).toBeInTheDocument();
    expect(screen.getByTestId('text-regions')).toBeInTheDocument();

    // Check that text regions are displayed
    expect(screen.getByTestId('region-region-1')).toBeInTheDocument();
    expect(screen.getByTestId('region-region-2')).toBeInTheDocument();
    expect(screen.getByText('Original: こんにちは')).toBeInTheDocument();
    expect(screen.getByText('Original: 世界')).toBeInTheDocument();

    // Translate first region
    await user.click(screen.getByTestId('translate-region-1'));

    await waitFor(() => {
      expect(screen.getByText('Translated: Hello')).toBeInTheDocument();
    });
    expect(mockApiCalls.startTranslation).toHaveBeenCalledWith(
      expect.objectContaining({
        region_id: 'region-1',
        text: 'こんにちは'
      })
    );

    // Translate second region
    await user.click(screen.getByTestId('translate-region-2'));

    await waitFor(() => {
      expect(screen.getByText('Translated: World')).toBeInTheDocument();
      expect(screen.getByTestId('current-step')).toHaveTextContent('completed');
    });

    // Step 5: Completion
    expect(screen.getByTestId('completed-step')).toBeInTheDocument();
    expect(screen.getByText('Translation Complete!')).toBeInTheDocument();
  });

  it('should handle error states in the workflow', async () => {
    const user = userEvent.setup();

    // Mock API failure
    mockApiCalls.createProject.mockRejectedValue(new Error('API Error'));

    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => { });

    render(
      <StateProvider>
        <WorkflowTestApp />
      </StateProvider>
    );

    await user.click(screen.getByTestId('create-project-btn'));

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('Failed to create project:', expect.any(Error));
    });

    // Should remain on the same step
    expect(screen.getByTestId('current-step')).toHaveTextContent('project-creation');

    consoleSpy.mockRestore();
  });

  it('should handle file upload validation', async () => {
    const user = userEvent.setup();

    const FileUploadTest: React.FC = () => {
      const [selectedFile, setSelectedFile] = React.useState<File | null>(null);
      const [error, setError] = React.useState<string>('');

      const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (!file) return;

        // Validate file type
        if (!file.type.startsWith('image/')) {
          setError('Please select an image file');
          return;
        }

        // Validate file size (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
          setError('File size must be less than 10MB');
          return;
        }

        setError('');
        setSelectedFile(file);
      };

      return (
        <div>
          <input
            type="file"
            accept="image/*"
            onChange={handleFileSelect}
            data-testid="file-input"
          />
          {error && <div data-testid="file-error">{error}</div>}
          {selectedFile && (
            <div data-testid="selected-file">
              Selected: {selectedFile.name}
            </div>
          )}
        </div>
      );
    };

    render(<FileUploadTest />);

    const fileInput = screen.getByTestId('file-input');

    // Test valid image file
    const validFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    await user.upload(fileInput, validFile);

    expect(screen.getByTestId('selected-file')).toHaveTextContent('Selected: test.jpg');
    expect(screen.queryByTestId('file-error')).not.toBeInTheDocument();

    // Test invalid file type
    const invalidFile = new File(['test'], 'test.txt', { type: 'text/plain' });
    await user.upload(fileInput, invalidFile);

    expect(screen.getByTestId('file-error')).toHaveTextContent('Please select an image file');
  });

  it('should handle canvas interactions in the workflow', async () => {
    const user = userEvent.setup();

    const CanvasInteractionTest: React.FC = () => {
      const [selectedTool, setSelectedTool] = React.useState('select');
      const [zoom, setZoom] = React.useState(1);
      const [regions, setRegions] = React.useState<any[]>([]);

      const handleCreateRegion = () => {
        const newRegion = {
          id: `region-${Date.now()}`,
          x: 0.2,
          y: 0.2,
          width: 0.15,
          height: 0.08,
          original_text: '',
          translated_text: '',
          region_type: 'speech_bubble',
          translation_status: 'pending'
        };
        setRegions(prev => [...prev, newRegion]);
      };

      return (
        <div>
          <div data-testid="canvas-controls">
            <button
              onClick={() => setSelectedTool('select')}
              data-testid="select-tool"
              className={selectedTool === 'select' ? 'active' : ''}
            >
              Select
            </button>
            <button
              onClick={() => setSelectedTool('text-region')}
              data-testid="text-region-tool"
              className={selectedTool === 'text-region' ? 'active' : ''}
            >
              Text Region
            </button>
            <button onClick={() => setZoom(zoom * 1.2)} data-testid="zoom-in">
              Zoom In
            </button>
            <button onClick={() => setZoom(zoom / 1.2)} data-testid="zoom-out">
              Zoom Out
            </button>
          </div>

          <div data-testid="canvas-area" onClick={selectedTool === 'text-region' ? handleCreateRegion : undefined}>
            <div data-testid="current-tool">Tool: {selectedTool}</div>
            <div data-testid="current-zoom">Zoom: {Math.round(zoom * 100)}%</div>
            <div data-testid="region-count">Regions: {regions.length}</div>
          </div>
        </div>
      );
    };

    render(<CanvasInteractionTest />);

    // Test tool switching
    await user.click(screen.getByTestId('text-region-tool'));
    expect(screen.getByTestId('current-tool')).toHaveTextContent('Tool: text-region');

    // Test zoom controls
    await user.click(screen.getByTestId('zoom-in'));
    expect(screen.getByTestId('current-zoom')).toHaveTextContent('Zoom: 120%');

    await user.click(screen.getByTestId('zoom-out'));
    expect(screen.getByTestId('current-zoom')).toHaveTextContent('Zoom: 100%');

    // Test region creation
    await user.click(screen.getByTestId('canvas-area'));
    expect(screen.getByTestId('region-count')).toHaveTextContent('Regions: 1');
  });
});
