import {
  validateProject,
  validateFile,
  validateTextRegion,
  validateOCRConfig,
  validateTranslationConfig,
  validateCanvasState,
  validateBatch,
  createFormValidator
} from '@/lib/validation';

describe('Validation Functions', () => {
  describe('validateProject', () => {
    it('should validate a valid project', () => {
      const validProject = {
        name: 'Test Project',
        description: 'A test project',
        source_language: 'ja',
        target_language: 'en'
      };

      const result = validateProject(validProject);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject project without name', () => {
      const invalidProject = {
        description: 'A test project',
        source_language: 'ja',
        target_language: 'en'
      };

      const result = validateProject(invalidProject);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Project name is required');
    });

    it('should reject project with same source and target language', () => {
      const invalidProject = {
        name: 'Test Project',
        source_language: 'ja',
        target_language: 'ja'
      };

      const result = validateProject(invalidProject);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Source and target languages must be different');
    });

    it('should warn about long description', () => {
      const projectWithLongDescription = {
        name: 'Test Project',
        description: 'A'.repeat(600),
        source_language: 'ja',
        target_language: 'en'
      };

      const result = validateProject(projectWithLongDescription);
      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Description is quite long (over 500 characters)');
    });

    it('should reject project with too long name', () => {
      const invalidProject = {
        name: 'A'.repeat(150),
        source_language: 'ja',
        target_language: 'en'
      };

      const result = validateProject(invalidProject);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Project name must be less than 100 characters');
    });
  });

  describe('validateFile', () => {
    it('should validate a valid image file', () => {
      const validFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      
      const result = validateFile(validFile);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject non-image files', () => {
      const invalidFile = new File(['test'], 'test.txt', { type: 'text/plain' });
      
      const result = validateFile(invalidFile);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('File must be an image (JPEG, PNG, WebP, or GIF)');
    });

    it('should reject files that are too large', () => {
      // Create a mock file with large size
      const largeFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      Object.defineProperty(largeFile, 'size', { value: 60 * 1024 * 1024 }); // 60MB
      
      const result = validateFile(largeFile);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('File size must be less than 50MB');
    });

    it('should warn about large files', () => {
      const largeFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      Object.defineProperty(largeFile, 'size', { value: 15 * 1024 * 1024 }); // 15MB
      
      const result = validateFile(largeFile);
      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Large file size may affect performance');
    });

    it('should reject files with invalid characters in name', () => {
      const invalidFile = new File(['test'], 'test<>file.jpg', { type: 'image/jpeg' });
      
      const result = validateFile(invalidFile);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('File name contains invalid characters');
    });
  });

  describe('validateTextRegion', () => {
    it('should validate a valid text region', () => {
      const validRegion = {
        x: 0.1,
        y: 0.2,
        width: 0.3,
        height: 0.1,
        original_text: 'Hello',
        translated_text: 'こんにちは',
        region_type: 'speech_bubble'
      };

      const result = validateTextRegion(validRegion);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject region with invalid coordinates', () => {
      const invalidRegion = {
        x: -0.1,
        y: 1.5,
        width: 0.3,
        height: 0.1,
        region_type: 'speech_bubble'
      };

      const result = validateTextRegion(invalidRegion);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('X position must be between 0 and 1');
      expect(result.errors).toContain('Y position must be between 0 and 1');
    });

    it('should reject region that extends beyond boundaries', () => {
      const invalidRegion = {
        x: 0.8,
        y: 0.8,
        width: 0.5,
        height: 0.5,
        region_type: 'speech_bubble'
      };

      const result = validateTextRegion(invalidRegion);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Region extends beyond right boundary');
      expect(result.errors).toContain('Region extends beyond bottom boundary');
    });

    it('should reject invalid region type', () => {
      const invalidRegion = {
        x: 0.1,
        y: 0.1,
        width: 0.2,
        height: 0.1,
        region_type: 'invalid_type'
      };

      const result = validateTextRegion(invalidRegion);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid region type');
    });

    it('should warn about very small regions', () => {
      const smallRegion = {
        x: 0.1,
        y: 0.1,
        width: 0.01,
        height: 0.01,
        region_type: 'speech_bubble'
      };

      const result = validateTextRegion(smallRegion);
      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Region is very small and may be hard to see');
    });

    it('should warn about very large regions', () => {
      const largeRegion = {
        x: 0.1,
        y: 0.1,
        width: 0.8,
        height: 0.8,
        region_type: 'speech_bubble'
      };

      const result = validateTextRegion(largeRegion);
      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Region is very large');
    });
  });

  describe('validateOCRConfig', () => {
    it('should validate a valid OCR config', () => {
      const validConfig = {
        provider: 'tesseract',
        language: 'ja',
        confidence_threshold: 0.8
      };

      const result = validateOCRConfig(validConfig);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject config without provider', () => {
      const invalidConfig = {
        language: 'ja'
      };

      const result = validateOCRConfig(invalidConfig);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('OCR provider is required');
    });

    it('should reject invalid provider', () => {
      const invalidConfig = {
        provider: 'invalid_provider',
        language: 'ja'
      };

      const result = validateOCRConfig(invalidConfig);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid OCR provider');
    });

    it('should reject invalid confidence threshold', () => {
      const invalidConfig = {
        provider: 'tesseract',
        language: 'ja',
        confidence_threshold: 1.5
      };

      const result = validateOCRConfig(invalidConfig);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Confidence threshold must be between 0 and 1');
    });
  });

  describe('validateTranslationConfig', () => {
    it('should validate a valid translation config', () => {
      const validConfig = {
        provider: 'claude',
        source_language: 'ja',
        target_language: 'en',
        context: 'Manga translation'
      };

      const result = validateTranslationConfig(validConfig);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject config with same source and target language', () => {
      const invalidConfig = {
        provider: 'claude',
        source_language: 'ja',
        target_language: 'ja'
      };

      const result = validateTranslationConfig(invalidConfig);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Source and target languages must be different');
    });

    it('should warn about very long context', () => {
      const configWithLongContext = {
        provider: 'claude',
        source_language: 'ja',
        target_language: 'en',
        context: 'A'.repeat(2500)
      };

      const result = validateTranslationConfig(configWithLongContext);
      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Context is very long and may affect translation quality');
    });
  });

  describe('validateCanvasState', () => {
    it('should validate a valid canvas state', () => {
      const validState = {
        zoom: 1.5,
        panX: 100,
        panY: 200
      };

      const result = validateCanvasState(validState);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject invalid zoom values', () => {
      const invalidState = {
        zoom: -1
      };

      const result = validateCanvasState(invalidState);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Zoom must be a positive number');
    });

    it('should warn about extreme zoom values', () => {
      const extremeZoomState = {
        zoom: 15
      };

      const result = validateCanvasState(extremeZoomState);
      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Zoom level is very high');
    });
  });

  describe('validateBatch', () => {
    it('should validate multiple items', () => {
      const items = [
        { name: 'Project 1', source_language: 'ja', target_language: 'en' },
        { name: 'Project 2', source_language: 'ko', target_language: 'en' }
      ];

      const result = validateBatch(items, validateProject);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should collect errors from multiple items', () => {
      const items = [
        { name: '', source_language: 'ja', target_language: 'en' },
        { name: 'Project 2', source_language: 'ja', target_language: 'ja' }
      ];

      const result = validateBatch(items, validateProject);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Item 1: Project name is required');
      expect(result.errors).toContain('Item 2: Source and target languages must be different');
    });
  });

  describe('createFormValidator', () => {
    it('should create a form validator', () => {
      const validator = createFormValidator({
        name: (value) => validateProject({ name: value }),
        file: (value) => validateFile(value)
      });

      const formData = {
        name: 'Test Project',
        file: new File(['test'], 'test.jpg', { type: 'image/jpeg' })
      };

      const result = validator(formData);
      expect(result.isValid).toBe(true);
    });

    it('should collect errors from form fields', () => {
      const validator = createFormValidator({
        name: (value) => validateProject({ name: value }),
        file: (value) => validateFile(value)
      });

      const formData = {
        name: '',
        file: new File(['test'], 'test.txt', { type: 'text/plain' })
      };

      const result = validator(formData);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('Project name is required'))).toBe(true);
      expect(result.errors.some(error => error.includes('File must be an image'))).toBe(true);
    });
  });
});
