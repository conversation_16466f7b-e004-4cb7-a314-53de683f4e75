'use client';

// Validation result interface
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Project validation
export const validateProject = (data: {
  name?: string;
  description?: string;
  source_language?: string;
  target_language?: string;
}): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required fields
  if (!data.name || data.name.trim().length === 0) {
    errors.push('Project name is required');
  } else if (data.name.length > 100) {
    errors.push('Project name must be less than 100 characters');
  }

  if (!data.source_language) {
    errors.push('Source language is required');
  }

  if (!data.target_language) {
    errors.push('Target language is required');
  }

  if (data.source_language === data.target_language) {
    errors.push('Source and target languages must be different');
  }

  // Optional fields validation
  if (data.description && data.description.length > 500) {
    warnings.push('Description is quite long (over 500 characters)');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

// File validation
export const validateFile = (file: File): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // File type validation
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
  if (!allowedTypes.includes(file.type)) {
    errors.push('File must be an image (JPEG, PNG, WebP, or GIF)');
  }

  // File size validation (max 50MB)
  const maxSize = 50 * 1024 * 1024; // 50MB
  if (file.size > maxSize) {
    errors.push('File size must be less than 50MB');
  }

  // File size warnings
  const warningSize = 10 * 1024 * 1024; // 10MB
  if (file.size > warningSize && file.size <= maxSize) {
    warnings.push('Large file size may affect performance');
  }

  // File name validation
  if (file.name.length > 255) {
    errors.push('File name is too long');
  }

  const invalidChars = /[<>:"/\\|?*]/;
  if (invalidChars.test(file.name)) {
    errors.push('File name contains invalid characters');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

// Text region validation
export const validateTextRegion = (region: {
  x?: number;
  y?: number;
  width?: number;
  height?: number;
  original_text?: string;
  translated_text?: string;
  region_type?: string;
}): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Position validation
  if (typeof region.x !== 'number' || region.x < 0 || region.x > 1) {
    errors.push('X position must be between 0 and 1');
  }

  if (typeof region.y !== 'number' || region.y < 0 || region.y > 1) {
    errors.push('Y position must be between 0 and 1');
  }

  if (typeof region.width !== 'number' || region.width <= 0 || region.width > 1) {
    errors.push('Width must be between 0 and 1');
  }

  if (typeof region.height !== 'number' || region.height <= 0 || region.height > 1) {
    errors.push('Height must be between 0 and 1');
  }

  // Check if region is within bounds
  if (region.x && region.width && region.x + region.width > 1) {
    errors.push('Region extends beyond right boundary');
  }

  if (region.y && region.height && region.y + region.height > 1) {
    errors.push('Region extends beyond bottom boundary');
  }

  // Region type validation
  const validTypes = ['speech_bubble', 'thought_bubble', 'narration', 'sound_effect', 'sign'];
  if (region.region_type && !validTypes.includes(region.region_type)) {
    errors.push('Invalid region type');
  }

  // Text validation
  if (region.original_text && region.original_text.length > 1000) {
    warnings.push('Original text is very long');
  }

  if (region.translated_text && region.translated_text.length > 1000) {
    warnings.push('Translated text is very long');
  }

  // Size warnings
  if (region.width && region.height) {
    const area = region.width * region.height;
    if (area < 0.001) {
      warnings.push('Region is very small and may be hard to see');
    }
    if (area > 0.5) {
      warnings.push('Region is very large');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

// OCR configuration validation
export const validateOCRConfig = (config: {
  provider?: string;
  language?: string;
  confidence_threshold?: number;
}): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Provider validation
  const validProviders = ['tesseract', 'google_vision', 'azure_vision'];
  if (!config.provider) {
    errors.push('OCR provider is required');
  } else if (!validProviders.includes(config.provider)) {
    errors.push('Invalid OCR provider');
  }

  // Language validation
  if (!config.language) {
    errors.push('OCR language is required');
  }

  // Confidence threshold validation
  if (config.confidence_threshold !== undefined) {
    if (typeof config.confidence_threshold !== 'number' || 
        config.confidence_threshold < 0 || 
        config.confidence_threshold > 1) {
      errors.push('Confidence threshold must be between 0 and 1');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

// Translation configuration validation
export const validateTranslationConfig = (config: {
  provider?: string;
  source_language?: string;
  target_language?: string;
  context?: string;
}): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Provider validation
  const validProviders = ['claude', 'openai', 'google_translate', 'deepl'];
  if (!config.provider) {
    errors.push('Translation provider is required');
  } else if (!validProviders.includes(config.provider)) {
    errors.push('Invalid translation provider');
  }

  // Language validation
  if (!config.source_language) {
    errors.push('Source language is required');
  }

  if (!config.target_language) {
    errors.push('Target language is required');
  }

  if (config.source_language === config.target_language) {
    errors.push('Source and target languages must be different');
  }

  // Context validation
  if (config.context && config.context.length > 2000) {
    warnings.push('Context is very long and may affect translation quality');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

// Canvas state validation
export const validateCanvasState = (state: {
  zoom?: number;
  panX?: number;
  panY?: number;
}): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Zoom validation
  if (state.zoom !== undefined) {
    if (typeof state.zoom !== 'number' || state.zoom <= 0) {
      errors.push('Zoom must be a positive number');
    } else if (state.zoom < 0.1) {
      warnings.push('Zoom level is very low');
    } else if (state.zoom > 10) {
      warnings.push('Zoom level is very high');
    }
  }

  // Pan validation
  if (state.panX !== undefined && typeof state.panX !== 'number') {
    errors.push('Pan X must be a number');
  }

  if (state.panY !== undefined && typeof state.panY !== 'number') {
    errors.push('Pan Y must be a number');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

// Batch validation utility
export const validateBatch = <T>(
  items: T[],
  validator: (item: T) => ValidationResult
): ValidationResult => {
  const allErrors: string[] = [];
  const allWarnings: string[] = [];

  items.forEach((item, index) => {
    const result = validator(item);
    
    result.errors.forEach(error => {
      allErrors.push(`Item ${index + 1}: ${error}`);
    });
    
    result.warnings.forEach(warning => {
      allWarnings.push(`Item ${index + 1}: ${warning}`);
    });
  });

  return {
    isValid: allErrors.length === 0,
    errors: allErrors,
    warnings: allWarnings
  };
};

// Form validation helper
export const createFormValidator = <T extends Record<string, any>>(
  validators: Partial<Record<keyof T, (value: any) => ValidationResult>>
) => {
  return (formData: T): ValidationResult => {
    const allErrors: string[] = [];
    const allWarnings: string[] = [];

    Object.entries(validators).forEach(([field, validator]) => {
      if (validator) {
        const result = validator(formData[field]);
        
        result.errors.forEach(error => {
          allErrors.push(`${field}: ${error}`);
        });
        
        result.warnings.forEach(warning => {
          allWarnings.push(`${field}: ${warning}`);
        });
      }
    });

    return {
      isValid: allErrors.length === 0,
      errors: allErrors,
      warnings: allWarnings
    };
  };
};

export default {
  validateProject,
  validateFile,
  validateTextRegion,
  validateOCRConfig,
  validateTranslationConfig,
  validateCanvasState,
  validateBatch,
  createFormValidator
};
