/**
 * TypeScript type definitions for canvas functionality and UI state
 */

import * as fabric from 'fabric';
import { TextRegionType, TextRegionResponse, ProjectPageResponse } from './api';

// Canvas-specific interfaces
export interface CanvasState {
  zoom: number;
  panX: number;
  panY: number;
  isDrawing: boolean;
  selectedTool: CanvasTool;
  selectedRegion?: string; // text region ID
}

export enum CanvasTool {
  SELECT = 'select',
  TEXT_REGION = 'text_region',
  PAN = 'pan',
  ZOOM = 'zoom'
}

// Text region with canvas-specific properties
export interface CanvasTextRegion extends TextRegionResponse {
  // Canvas object reference
  fabricObject?: fabric.Rect;
  // UI state
  isSelected: boolean;
  isEditing: boolean;
  // Visual properties
  borderColor: string;
  fillOpacity: number;
}

// Canvas dimensions and image info
export interface CanvasImageInfo {
  originalWidth: number;
  originalHeight: number;
  displayWidth: number;
  displayHeight: number;
  scaleX: number;
  scaleY: number;
  imageUrl: string;
}

// Font configuration
export interface FontConfig {
  family: string;
  size: number;
  color: string;
  backgroundColor: string;
  weight: 'normal' | 'bold';
  style: 'normal' | 'italic';
  align: 'left' | 'center' | 'right';
}

// Canvas event handlers
export interface CanvasEventHandlers {
  onRegionCreate: (region: Partial<CanvasTextRegion>) => void;
  onRegionSelect: (regionId: string) => void;
  onRegionUpdate: (regionId: string, updates: Partial<CanvasTextRegion>) => void;
  onRegionDelete: (regionId: string) => void;
  onZoomChange: (zoom: number) => void;
  onPanChange: (panX: number, panY: number) => void;
}

// Canvas configuration
export interface CanvasConfig {
  minZoom: number;
  maxZoom: number;
  zoomStep: number;
  defaultRegionColor: string;
  selectedRegionColor: string;
  regionBorderWidth: number;
  regionFillOpacity: number;
}

// UI state for the translation interface
export interface TranslationUIState {
  // Current project and page
  currentProject?: ProjectPageResponse;
  currentPage?: ProjectPageResponse;

  // Canvas state
  canvas: CanvasState;

  // Text regions
  textRegions: CanvasTextRegion[];

  // UI panels
  showProjectPanel: boolean;
  showTextEditPanel: boolean;
  showOCRPanel: boolean;
  showTranslationPanel: boolean;

  // Loading states
  isLoadingProject: boolean;
  isLoadingPage: boolean;
  isProcessingOCR: boolean;
  isProcessingTranslation: boolean;

  // Error states
  error?: string;
}

// File upload types
export interface FileUploadState {
  isUploading: boolean;
  progress: number;
  error?: string;
}

export interface UploadedFile {
  file: File;
  preview: string;
  pageNumber: number;
}

// OCR processing state
export interface OCRProcessingState {
  isProcessing: boolean;
  progress: number;
  currentProvider: string;
  results: OCRResult[];
  error?: string;
}

export interface OCRResult {
  id: string;
  text: string;
  confidence: number;
  boundingBox: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  regionType: TextRegionType;
}

// Translation processing state
export interface TranslationProcessingState {
  isProcessing: boolean;
  progress: number;
  currentProvider: string;
  results: TranslationResult[];
  error?: string;
}

export interface TranslationResult {
  id: string;
  originalText: string;
  translatedText: string;
  confidence: number;
  alternatives: string[];
  isSelected: boolean;
}

// Keyboard shortcuts
export interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
  action: () => void;
  description: string;
}

// Canvas history for undo/redo
export interface CanvasHistoryState {
  states: string[]; // JSON serialized canvas states
  currentIndex: number;
  maxStates: number;
}

// Export/import types
export interface ExportOptions {
  format: 'png' | 'jpg' | 'pdf';
  quality: number;
  includeOriginal: boolean;
  includeTranslated: boolean;
  resolution: 'original' | 'high' | 'medium' | 'low';
}

export interface ImportOptions {
  mergeWithExisting: boolean;
  overwriteExisting: boolean;
  validateRegions: boolean;
}

// Validation types
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export interface ValidationError {
  field: string;
  message: string;
  severity: 'error' | 'warning';
}

export interface ValidationWarning {
  field: string;
  message: string;
  suggestion?: string;
}

// Performance monitoring
export interface PerformanceMetrics {
  canvasRenderTime: number;
  imageLoadTime: number;
  ocrProcessingTime: number;
  translationProcessingTime: number;
  memoryUsage: number;
}

// Theme and appearance
export interface ThemeConfig {
  mode: 'light' | 'dark';
  primaryColor: string;
  secondaryColor: string;
  backgroundColor: string;
  textColor: string;
  borderColor: string;
  accentColor: string;
}

// User preferences
export interface UserPreferences {
  theme: ThemeConfig;
  defaultFont: FontConfig;
  canvasConfig: CanvasConfig;
  keyboardShortcuts: KeyboardShortcut[];
  autoSave: boolean;
  autoSaveInterval: number; // in seconds
}

// Local storage keys
export const STORAGE_KEYS = {
  USER_PREFERENCES: 'ho-trans-user-preferences',
  CANVAS_STATE: 'ho-trans-canvas-state',
  RECENT_PROJECTS: 'ho-trans-recent-projects',
  DRAFT_REGIONS: 'ho-trans-draft-regions'
} as const;

// Default configurations
export const DEFAULT_CANVAS_CONFIG: CanvasConfig = {
  minZoom: 0.1,
  maxZoom: 5.0,
  zoomStep: 0.1,
  defaultRegionColor: '#3b82f6',
  selectedRegionColor: '#ef4444',
  regionBorderWidth: 2,
  regionFillOpacity: 0.2
};

export const DEFAULT_FONT_CONFIG: FontConfig = {
  family: 'Arial',
  size: 14,
  color: '#000000',
  backgroundColor: 'transparent',
  weight: 'normal',
  style: 'normal',
  align: 'center'
};

export const DEFAULT_THEME_CONFIG: ThemeConfig = {
  mode: 'light',
  primaryColor: '#3b82f6',
  secondaryColor: '#6b7280',
  backgroundColor: '#ffffff',
  textColor: '#1f2937',
  borderColor: '#d1d5db',
  accentColor: '#10b981'
};
