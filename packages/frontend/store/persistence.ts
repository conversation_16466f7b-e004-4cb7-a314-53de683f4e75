'use client';

import { AppState } from './AppContext';
import { CanvasTextRegion } from '@/types/canvas';

// Storage keys
const STORAGE_KEYS = {
  APP_STATE: 'ho-trans-app-state',
  USER_PREFERENCES: 'ho-trans-preferences',
  UI_STATE: 'ho-trans-ui-state',
  CANVAS_STATE: 'ho-trans-canvas-state',
  TEXT_REGIONS: 'ho-trans-text-regions',
  PROJECT_CACHE: 'ho-trans-project-cache',
  SESSION_DATA: 'ho-trans-session'
} as const;

// Storage interface
interface StorageInterface {
  getItem(key: string): string | null;
  setItem(key: string, value: string): void;
  removeItem(key: string): void;
  clear(): void;
}

// Safe storage wrapper that handles SSR and storage errors
class SafeStorage implements StorageInterface {
  private storage: Storage | null = null;

  constructor() {
    if (typeof window !== 'undefined') {
      try {
        this.storage = window.localStorage;
        // Test if storage is available
        const testKey = '__storage_test__';
        this.storage.setItem(testKey, 'test');
        this.storage.removeItem(testKey);
      } catch (error) {
        console.warn('localStorage is not available:', error);
        this.storage = null;
      }
    }
  }

  getItem(key: string): string | null {
    if (!this.storage) return null;
    try {
      return this.storage.getItem(key);
    } catch (error) {
      console.error('Error reading from localStorage:', error);
      return null;
    }
  }

  setItem(key: string, value: string): void {
    if (!this.storage) return;
    try {
      this.storage.setItem(key, value);
    } catch (error) {
      console.error('Error writing to localStorage:', error);
      // Handle quota exceeded error
      if (error instanceof DOMException && error.code === 22) {
        this.clearOldData();
        try {
          this.storage.setItem(key, value);
        } catch (retryError) {
          console.error('Failed to save after clearing old data:', retryError);
        }
      }
    }
  }

  removeItem(key: string): void {
    if (!this.storage) return;
    try {
      this.storage.removeItem(key);
    } catch (error) {
      console.error('Error removing from localStorage:', error);
    }
  }

  clear(): void {
    if (!this.storage) return;
    try {
      this.storage.clear();
    } catch (error) {
      console.error('Error clearing localStorage:', error);
    }
  }

  private clearOldData(): void {
    if (!this.storage) return;
    
    // Remove old session data and temporary cache
    const keysToRemove = [];
    for (let i = 0; i < this.storage.length; i++) {
      const key = this.storage.key(i);
      if (key && (key.includes('temp-') || key.includes('cache-'))) {
        keysToRemove.push(key);
      }
    }
    
    keysToRemove.forEach(key => this.storage!.removeItem(key));
  }
}

const storage = new SafeStorage();

// Serialization utilities
const serialize = <T>(data: T): string => {
  try {
    return JSON.stringify(data, (key, value) => {
      // Handle Date objects
      if (value instanceof Date) {
        return { __type: 'Date', value: value.toISOString() };
      }
      return value;
    });
  } catch (error) {
    console.error('Error serializing data:', error);
    return '{}';
  }
};

const deserialize = <T>(data: string): T | null => {
  try {
    return JSON.parse(data, (key, value) => {
      // Handle Date objects
      if (value && typeof value === 'object' && value.__type === 'Date') {
        return new Date(value.value);
      }
      return value;
    });
  } catch (error) {
    console.error('Error deserializing data:', error);
    return null;
  }
};

// Persistence functions
export const persistenceUtils = {
  // Save user preferences
  savePreferences: (preferences: AppState['preferences']): void => {
    storage.setItem(STORAGE_KEYS.USER_PREFERENCES, serialize(preferences));
  },

  // Load user preferences
  loadPreferences: (): AppState['preferences'] | null => {
    const data = storage.getItem(STORAGE_KEYS.USER_PREFERENCES);
    return data ? deserialize<AppState['preferences']>(data) : null;
  },

  // Save UI state
  saveUIState: (uiState: AppState['ui']): void => {
    storage.setItem(STORAGE_KEYS.UI_STATE, serialize(uiState));
  },

  // Load UI state
  loadUIState: (): AppState['ui'] | null => {
    const data = storage.getItem(STORAGE_KEYS.UI_STATE);
    return data ? deserialize<AppState['ui']>(data) : null;
  },

  // Save canvas state
  saveCanvasState: (canvasState: AppState['canvas']): void => {
    storage.setItem(STORAGE_KEYS.CANVAS_STATE, serialize(canvasState));
  },

  // Load canvas state
  loadCanvasState: (): AppState['canvas'] | null => {
    const data = storage.getItem(STORAGE_KEYS.CANVAS_STATE);
    return data ? deserialize<AppState['canvas']>(data) : null;
  },

  // Save text regions for current page
  saveTextRegions: (pageId: string, textRegions: CanvasTextRegion[]): void => {
    const key = `${STORAGE_KEYS.TEXT_REGIONS}-${pageId}`;
    storage.setItem(key, serialize(textRegions));
  },

  // Load text regions for current page
  loadTextRegions: (pageId: string): CanvasTextRegion[] | null => {
    const key = `${STORAGE_KEYS.TEXT_REGIONS}-${pageId}`;
    const data = storage.getItem(key);
    return data ? deserialize<CanvasTextRegion[]>(data) : null;
  },

  // Save session data (temporary data that expires)
  saveSessionData: (sessionData: any): void => {
    const dataWithTimestamp = {
      ...sessionData,
      timestamp: Date.now(),
      expires: Date.now() + (24 * 60 * 60 * 1000) // 24 hours
    };
    storage.setItem(STORAGE_KEYS.SESSION_DATA, serialize(dataWithTimestamp));
  },

  // Load session data (check expiration)
  loadSessionData: (): any | null => {
    const data = storage.getItem(STORAGE_KEYS.SESSION_DATA);
    if (!data) return null;

    const sessionData = deserialize<any>(data);
    if (!sessionData || !sessionData.expires) return null;

    // Check if expired
    if (Date.now() > sessionData.expires) {
      storage.removeItem(STORAGE_KEYS.SESSION_DATA);
      return null;
    }

    return sessionData;
  },

  // Cache project data temporarily
  cacheProjectData: (projectId: string, projectData: any): void => {
    const key = `${STORAGE_KEYS.PROJECT_CACHE}-${projectId}`;
    const dataWithTimestamp = {
      ...projectData,
      timestamp: Date.now(),
      expires: Date.now() + (60 * 60 * 1000) // 1 hour
    };
    storage.setItem(key, serialize(dataWithTimestamp));
  },

  // Load cached project data
  loadCachedProjectData: (projectId: string): any | null => {
    const key = `${STORAGE_KEYS.PROJECT_CACHE}-${projectId}`;
    const data = storage.getItem(key);
    if (!data) return null;

    const projectData = deserialize<any>(data);
    if (!projectData || !projectData.expires) return null;

    // Check if expired
    if (Date.now() > projectData.expires) {
      storage.removeItem(key);
      return null;
    }

    return projectData;
  },

  // Clear all cached data
  clearCache: (): void => {
    const keysToRemove = [];
    
    if (typeof window !== 'undefined' && window.localStorage) {
      for (let i = 0; i < window.localStorage.length; i++) {
        const key = window.localStorage.key(i);
        if (key && (key.includes(STORAGE_KEYS.PROJECT_CACHE) || key.includes('temp-'))) {
          keysToRemove.push(key);
        }
      }
    }

    keysToRemove.forEach(key => storage.removeItem(key));
  },

  // Clear all app data
  clearAllData: (): void => {
    Object.values(STORAGE_KEYS).forEach(key => {
      storage.removeItem(key);
    });
    persistenceUtils.clearCache();
  },

  // Export app data for backup
  exportData: (): string => {
    const exportData: Record<string, any> = {};
    
    Object.entries(STORAGE_KEYS).forEach(([name, key]) => {
      const data = storage.getItem(key);
      if (data) {
        exportData[name] = data;
      }
    });

    return serialize({
      version: '1.0',
      timestamp: new Date().toISOString(),
      data: exportData
    });
  },

  // Import app data from backup
  importData: (backupData: string): boolean => {
    try {
      const parsed = deserialize<any>(backupData);
      if (!parsed || !parsed.data) {
        throw new Error('Invalid backup data format');
      }

      // Clear existing data
      persistenceUtils.clearAllData();

      // Import data
      Object.entries(parsed.data).forEach(([name, data]) => {
        const key = STORAGE_KEYS[name as keyof typeof STORAGE_KEYS];
        if (key && typeof data === 'string') {
          storage.setItem(key, data);
        }
      });

      return true;
    } catch (error) {
      console.error('Error importing data:', error);
      return false;
    }
  },

  // Get storage usage info
  getStorageInfo: (): { used: number; available: number; percentage: number } => {
    if (typeof window === 'undefined' || !window.localStorage) {
      return { used: 0, available: 0, percentage: 0 };
    }

    try {
      let used = 0;
      for (let i = 0; i < window.localStorage.length; i++) {
        const key = window.localStorage.key(i);
        if (key) {
          const value = window.localStorage.getItem(key);
          if (value) {
            used += key.length + value.length;
          }
        }
      }

      // Estimate available space (most browsers have ~5-10MB limit)
      const estimated = 5 * 1024 * 1024; // 5MB
      const percentage = (used / estimated) * 100;

      return {
        used,
        available: estimated - used,
        percentage: Math.min(percentage, 100)
      };
    } catch (error) {
      console.error('Error calculating storage usage:', error);
      return { used: 0, available: 0, percentage: 0 };
    }
  }
};

// Auto-save functionality
export class AutoSave {
  private saveInterval: NodeJS.Timeout | null = null;
  private pendingChanges = new Set<string>();

  constructor(
    private saveFunction: (changes: string[]) => void,
    private intervalMs: number = 30000 // 30 seconds
  ) {}

  // Mark data as changed
  markChanged(key: string): void {
    this.pendingChanges.add(key);
    this.scheduleSave();
  }

  // Schedule auto-save
  private scheduleSave(): void {
    if (this.saveInterval) {
      clearTimeout(this.saveInterval);
    }

    this.saveInterval = setTimeout(() => {
      if (this.pendingChanges.size > 0) {
        const changes = Array.from(this.pendingChanges);
        this.pendingChanges.clear();
        this.saveFunction(changes);
      }
    }, this.intervalMs);
  }

  // Force immediate save
  forceSave(): void {
    if (this.saveInterval) {
      clearTimeout(this.saveInterval);
      this.saveInterval = null;
    }

    if (this.pendingChanges.size > 0) {
      const changes = Array.from(this.pendingChanges);
      this.pendingChanges.clear();
      this.saveFunction(changes);
    }
  }

  // Stop auto-save
  stop(): void {
    if (this.saveInterval) {
      clearTimeout(this.saveInterval);
      this.saveInterval = null;
    }
    this.pendingChanges.clear();
  }
}

export default persistenceUtils;
