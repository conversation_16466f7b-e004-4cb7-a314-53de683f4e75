'use client';

import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { ProjectResponse, ProjectPageResponse } from '@/types/api';
import { CanvasTextRegion, CanvasState, CanvasTool } from '@/types/canvas';

// Application State Interface
export interface AppState {
  // Project Management
  projects: ProjectResponse[];
  currentProject: ProjectResponse | null;
  currentPage: ProjectPageResponse | null;

  // Canvas State
  canvas: CanvasState;
  textRegions: CanvasTextRegion[];
  selectedRegionIds: string[];

  // UI State
  ui: {
    showProjectPanel: boolean;
    showTextEditPanel: boolean;
    showOCRPanel: boolean;
    showTranslationPanel: boolean;
    showGrid: boolean;
    showRulers: boolean;
    sidebarWidth: number;
    rightPanelWidth: number;
  };

  // User Preferences
  preferences: {
    theme: 'light' | 'dark';
    language: string;
    autoSave: boolean;
    gridSize: number;
    defaultFontFamily: string;
    defaultFontSize: number;
    defaultFontColor: string;
    defaultBackgroundColor: string;
  };

  // Loading and Error States
  loading: {
    projects: boolean;
    currentProject: boolean;
    currentPage: boolean;
    ocr: boolean;
    translation: boolean;
    saving: boolean;
  };

  errors: {
    projects: string | null;
    currentProject: string | null;
    currentPage: string | null;
    ocr: string | null;
    translation: string | null;
    saving: string | null;
  };

  // History for Undo/Redo
  history: {
    past: CanvasTextRegion[][];
    present: CanvasTextRegion[];
    future: CanvasTextRegion[][];
    maxHistorySize: number;
  };
}

// Action Types
export type AppAction =
  // Project Actions
  | { type: 'SET_PROJECTS'; payload: ProjectResponse[] }
  | { type: 'SET_CURRENT_PROJECT'; payload: ProjectResponse | null }
  | { type: 'SET_CURRENT_PAGE'; payload: ProjectPageResponse | null }
  | { type: 'ADD_PROJECT'; payload: ProjectResponse }
  | { type: 'UPDATE_PROJECT'; payload: { id: string; updates: Partial<ProjectResponse> } }
  | { type: 'DELETE_PROJECT'; payload: string }

  // Canvas Actions
  | { type: 'UPDATE_CANVAS_STATE'; payload: Partial<CanvasState> }
  | { type: 'SET_CANVAS_TOOL'; payload: CanvasTool }
  | { type: 'SET_ZOOM'; payload: number }
  | { type: 'SET_PAN'; payload: { x: number; y: number } }

  // Text Region Actions
  | { type: 'SET_TEXT_REGIONS'; payload: CanvasTextRegion[] }
  | { type: 'ADD_TEXT_REGION'; payload: CanvasTextRegion }
  | { type: 'UPDATE_TEXT_REGION'; payload: { id: string; updates: Partial<CanvasTextRegion> } }
  | { type: 'DELETE_TEXT_REGION'; payload: string }
  | { type: 'SELECT_TEXT_REGIONS'; payload: string[] }
  | { type: 'DESELECT_ALL_REGIONS' }

  // UI Actions
  | { type: 'TOGGLE_PANEL'; payload: keyof AppState['ui'] }
  | { type: 'SET_PANEL_VISIBILITY'; payload: { panel: keyof AppState['ui']; visible: boolean } }
  | { type: 'SET_SIDEBAR_WIDTH'; payload: number }
  | { type: 'SET_RIGHT_PANEL_WIDTH'; payload: number }

  // Preferences Actions
  | { type: 'UPDATE_PREFERENCES'; payload: Partial<AppState['preferences']> }
  | { type: 'RESET_PREFERENCES' }

  // Loading Actions
  | { type: 'SET_LOADING'; payload: { key: keyof AppState['loading']; loading: boolean } }

  // Error Actions
  | { type: 'SET_ERROR'; payload: { key: keyof AppState['errors']; error: string | null } }
  | { type: 'CLEAR_ERRORS' }

  // History Actions
  | { type: 'PUSH_HISTORY'; payload: CanvasTextRegion[] }
  | { type: 'UNDO' }
  | { type: 'REDO' }
  | { type: 'CLEAR_HISTORY' };

// Initial State
const initialState: AppState = {
  projects: [],
  currentProject: null,
  currentPage: null,

  canvas: {
    zoom: 1,
    panX: 0,
    panY: 0,
    isDrawing: false,
    selectedTool: CanvasTool.SELECT
  },

  textRegions: [],
  selectedRegionIds: [],

  ui: {
    showProjectPanel: true,
    showTextEditPanel: true,
    showOCRPanel: false,
    showTranslationPanel: false,
    showGrid: false,
    showRulers: false,
    sidebarWidth: 320,
    rightPanelWidth: 320
  },

  preferences: {
    theme: 'light',
    language: 'en',
    autoSave: true,
    gridSize: 20,
    defaultFontFamily: 'Arial',
    defaultFontSize: 14,
    defaultFontColor: '#000000',
    defaultBackgroundColor: 'transparent'
  },

  loading: {
    projects: false,
    currentProject: false,
    currentPage: false,
    ocr: false,
    translation: false,
    saving: false
  },

  errors: {
    projects: null,
    currentProject: null,
    currentPage: null,
    ocr: null,
    translation: null,
    saving: null
  },

  history: {
    past: [],
    present: [],
    future: [],
    maxHistorySize: 50
  }
};

// Reducer Function
function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    // Project Actions
    case 'SET_PROJECTS':
      return { ...state, projects: action.payload };

    case 'SET_CURRENT_PROJECT':
      return { ...state, currentProject: action.payload };

    case 'SET_CURRENT_PAGE':
      return { ...state, currentPage: action.payload };

    case 'ADD_PROJECT':
      return { ...state, projects: [action.payload, ...state.projects] };

    case 'UPDATE_PROJECT':
      return {
        ...state,
        projects: state.projects.map(project =>
          project.id === action.payload.id
            ? { ...project, ...action.payload.updates }
            : project
        ),
        currentProject: state.currentProject?.id === action.payload.id
          ? { ...state.currentProject, ...action.payload.updates }
          : state.currentProject
      };

    case 'DELETE_PROJECT':
      return {
        ...state,
        projects: state.projects.filter(project => project.id !== action.payload),
        currentProject: state.currentProject?.id === action.payload ? null : state.currentProject
      };

    // Canvas Actions
    case 'UPDATE_CANVAS_STATE':
      return {
        ...state,
        canvas: { ...state.canvas, ...action.payload }
      };

    case 'SET_CANVAS_TOOL':
      return {
        ...state,
        canvas: { ...state.canvas, selectedTool: action.payload }
      };

    case 'SET_ZOOM':
      return {
        ...state,
        canvas: { ...state.canvas, zoom: action.payload }
      };

    case 'SET_PAN':
      return {
        ...state,
        canvas: { ...state.canvas, panX: action.payload.x, panY: action.payload.y }
      };

    // Text Region Actions
    case 'SET_TEXT_REGIONS':
      return {
        ...state,
        textRegions: action.payload,
        history: {
          ...state.history,
          present: action.payload
        }
      };

    case 'ADD_TEXT_REGION':
      const newRegions = [...state.textRegions, action.payload];
      return {
        ...state,
        textRegions: newRegions,
        history: {
          past: [...state.history.past, state.history.present].slice(-state.history.maxHistorySize),
          present: newRegions,
          future: [],
          maxHistorySize: state.history.maxHistorySize
        }
      };

    case 'UPDATE_TEXT_REGION':
      const updatedRegions = state.textRegions.map(region =>
        region.id === action.payload.id
          ? { ...region, ...action.payload.updates, updated_at: new Date().toISOString() }
          : region
      );
      return {
        ...state,
        textRegions: updatedRegions,
        history: {
          past: [...state.history.past, state.history.present].slice(-state.history.maxHistorySize),
          present: updatedRegions,
          future: [],
          maxHistorySize: state.history.maxHistorySize
        }
      };

    case 'DELETE_TEXT_REGION':
      const filteredRegions = state.textRegions.filter(region => region.id !== action.payload);
      return {
        ...state,
        textRegions: filteredRegions,
        selectedRegionIds: state.selectedRegionIds.filter(id => id !== action.payload),
        history: {
          past: [...state.history.past, state.history.present].slice(-state.history.maxHistorySize),
          present: filteredRegions,
          future: [],
          maxHistorySize: state.history.maxHistorySize
        }
      };

    case 'SELECT_TEXT_REGIONS':
      return {
        ...state,
        selectedRegionIds: action.payload,
        textRegions: state.textRegions.map(region => ({
          ...region,
          isSelected: action.payload.includes(region.id)
        }))
      };

    case 'DESELECT_ALL_REGIONS':
      return {
        ...state,
        selectedRegionIds: [],
        textRegions: state.textRegions.map(region => ({
          ...region,
          isSelected: false
        }))
      };

    // UI Actions
    case 'TOGGLE_PANEL':
      return {
        ...state,
        ui: {
          ...state.ui,
          [action.payload]: !state.ui[action.payload]
        }
      };

    case 'SET_PANEL_VISIBILITY':
      return {
        ...state,
        ui: {
          ...state.ui,
          [action.payload.panel]: action.payload.visible
        }
      };

    case 'SET_SIDEBAR_WIDTH':
      return {
        ...state,
        ui: { ...state.ui, sidebarWidth: action.payload }
      };

    case 'SET_RIGHT_PANEL_WIDTH':
      return {
        ...state,
        ui: { ...state.ui, rightPanelWidth: action.payload }
      };

    // Preferences Actions
    case 'UPDATE_PREFERENCES':
      return {
        ...state,
        preferences: { ...state.preferences, ...action.payload }
      };

    case 'RESET_PREFERENCES':
      return {
        ...state,
        preferences: initialState.preferences
      };

    // Loading Actions
    case 'SET_LOADING':
      return {
        ...state,
        loading: { ...state.loading, [action.payload.key]: action.payload.loading }
      };

    // Error Actions
    case 'SET_ERROR':
      return {
        ...state,
        errors: { ...state.errors, [action.payload.key]: action.payload.error }
      };

    case 'CLEAR_ERRORS':
      return {
        ...state,
        errors: initialState.errors
      };

    // History Actions
    case 'PUSH_HISTORY':
      return {
        ...state,
        history: {
          past: [...state.history.past, state.history.present].slice(-state.history.maxHistorySize),
          present: action.payload,
          future: [],
          maxHistorySize: state.history.maxHistorySize
        }
      };

    case 'UNDO':
      if (state.history.past.length === 0) return state;
      const previous = state.history.past[state.history.past.length - 1];
      const newPast = state.history.past.slice(0, state.history.past.length - 1);
      return {
        ...state,
        textRegions: previous,
        history: {
          past: newPast,
          present: previous,
          future: [state.history.present, ...state.history.future],
          maxHistorySize: state.history.maxHistorySize
        }
      };

    case 'REDO':
      if (state.history.future.length === 0) return state;
      const next = state.history.future[0];
      const newFuture = state.history.future.slice(1);
      return {
        ...state,
        textRegions: next,
        history: {
          past: [...state.history.past, state.history.present],
          present: next,
          future: newFuture,
          maxHistorySize: state.history.maxHistorySize
        }
      };

    case 'CLEAR_HISTORY':
      return {
        ...state,
        history: {
          past: [],
          present: state.textRegions,
          future: [],
          maxHistorySize: state.history.maxHistorySize
        }
      };

    default:
      return state;
  }
}

// Context
const AppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
} | null>(null);

// Provider Component
export const AppProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  const value = {
    state,
    dispatch
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};

// Hook to use the context
export const useAppContext = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
};

export default AppContext;
